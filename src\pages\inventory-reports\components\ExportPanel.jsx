import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ExportPanel = ({ onExport, onSchedule, currentLanguage }) => {
  const [isScheduleModalOpen, setIsScheduleModalOpen] = useState(false);
  const [scheduleSettings, setScheduleSettings] = useState({
    frequency: 'weekly',
    day: 'monday',
    time: '09:00',
    format: 'excel',
    recipients: ['']
  });

  const exportFormats = [
    {
      id: 'pdf',
      name: 'PDF',
      description: currentLanguage === 'ar' ? 'تقرير بتنسيق PDF للطباعة والعرض' : 'PDF format for printing and presentation',
      icon: 'FileText',
      size: currentLanguage === 'ar' ? 'صغير (~2MB)' : 'Small (~2MB)'
    },
    {
      id: 'excel',
      name: 'Excel',
      description: currentLanguage === 'ar' ? 'جدول بيانات Excel للتحليل المفصل' : 'Excel spreadsheet for detailed analysis',
      icon: 'Table',
      size: currentLanguage === 'ar' ? 'متوسط (~5MB)' : 'Medium (~5MB)'
    },
    {
      id: 'csv',
      name: 'CSV',
      description: currentLanguage === 'ar' ? 'بيانات CSV للتكامل مع الأنظمة الأخرى' : 'CSV data for integration with other systems',
      icon: 'Database',
      size: currentLanguage === 'ar' ? 'صغير (~1MB)' : 'Small (~1MB)'
    },
    {
      id: 'json',
      name: 'JSON',
      description: currentLanguage === 'ar' ? 'بيانات JSON للمطورين والتكامل مع APIs' : 'JSON data for developers and API integration',
      icon: 'Code',
      size: currentLanguage === 'ar' ? 'صغير (~1MB)' : 'Small (~1MB)'
    }
  ];

  const scheduleFrequencies = [
    { value: 'daily', label: currentLanguage === 'ar' ? 'يومياً' : 'Daily' },
    { value: 'weekly', label: currentLanguage === 'ar' ? 'أسبوعياً' : 'Weekly' },
    { value: 'monthly', label: currentLanguage === 'ar' ? 'شهرياً' : 'Monthly' },
    { value: 'quarterly', label: currentLanguage === 'ar' ? 'كل ثلاثة أشهر' : 'Quarterly' }
  ];

  const weekDays = [
    { value: 'monday', label: currentLanguage === 'ar' ? 'الاثنين' : 'Monday' },
    { value: 'tuesday', label: currentLanguage === 'ar' ? 'الثلاثاء' : 'Tuesday' },
    { value: 'wednesday', label: currentLanguage === 'ar' ? 'الأربعاء' : 'Wednesday' },
    { value: 'thursday', label: currentLanguage === 'ar' ? 'الخميس' : 'Thursday' },
    { value: 'friday', label: currentLanguage === 'ar' ? 'الجمعة' : 'Friday' },
    { value: 'saturday', label: currentLanguage === 'ar' ? 'السبت' : 'Saturday' },
    { value: 'sunday', label: currentLanguage === 'ar' ? 'الأحد' : 'Sunday' }
  ];

  const handleExportClick = (format) => {
    onExport(format);
    // Show success message or loading state
    console.log(`Exporting in ${format} format...`);
  };

  const handleScheduleSubmit = (e) => {
    e.preventDefault();
    onSchedule(scheduleSettings);
    setIsScheduleModalOpen(false);
    // Show success message
    console.log('Report scheduled successfully:', scheduleSettings);
  };

  const addRecipient = () => {
    setScheduleSettings(prev => ({
      ...prev,
      recipients: [...prev.recipients, '']
    }));
  };

  const updateRecipient = (index, email) => {
    setScheduleSettings(prev => ({
      ...prev,
      recipients: prev.recipients.map((recipient, i) => i === index ? email : recipient)
    }));
  };

  const removeRecipient = (index) => {
    setScheduleSettings(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="space-y-6">
      {/* Export Options */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-text-primary">
              {currentLanguage === 'ar' ? 'تصدير التقرير' : 'Export Report'}
            </h3>
            <p className="text-sm text-text-secondary mt-1">
              {currentLanguage === 'ar' ? 'حدد التنسيق المطلوب لتصدير التقرير' : 'Choose the format to export your report'}
            </p>
          </div>
          <Icon name="Download" size={24} className="text-primary" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {exportFormats.map((format) => (
            <div
              key={format.id}
              className="border border-border rounded-lg p-4 hover:border-primary hover:bg-primary-50 transition-all cursor-pointer group"
              onClick={() => handleExportClick(format.id)}
            >
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                  <Icon name={format.icon} size={20} className="text-primary" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-semibold text-text-primary">{format.name}</h4>
                    <span className="text-xs text-text-secondary">{format.size}</span>
                  </div>
                  <p className="text-xs text-text-secondary leading-relaxed">
                    {format.description}
                  </p>
                  <Button
                    variant="ghost"
                    size="sm"
                    iconName="Download"
                    className="mt-3 text-primary hover:bg-primary-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleExportClick(format.id);
                    }}
                  >
                    {currentLanguage === 'ar' ? 'تنزيل' : 'Download'}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Scheduled Reports */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-text-primary">
              {currentLanguage === 'ar' ? 'التقارير المجدولة' : 'Scheduled Reports'}
            </h3>
            <p className="text-sm text-text-secondary mt-1">
              {currentLanguage === 'ar' ? 'جدولة التقارير للإرسال التلقائي' : 'Schedule reports for automatic delivery'}
            </p>
          </div>
          <Button
            variant="primary"
            iconName="Calendar"
            onClick={() => setIsScheduleModalOpen(true)}
          >
            {currentLanguage === 'ar' ? 'جدولة تقرير' : 'Schedule Report'}
          </Button>
        </div>

        {/* Existing scheduled reports */}
        <div className="space-y-3">
          <div className="bg-background border border-border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
                  <Icon name="Calendar" size={16} className="text-success" />
                </div>
                <div>
                  <p className="text-sm font-medium text-text-primary">
                    {currentLanguage === 'ar' ? 'تقرير المخزون الأسبوعي' : 'Weekly Inventory Report'}
                  </p>
                  <p className="text-xs text-text-secondary">
                    {currentLanguage === 'ar' ? 'كل يوم اثنين الساعة 9:00 صباحاً - Excel' : 'Every Monday at 9:00 AM - Excel'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <span className="px-2 py-1 bg-success-100 text-success text-xs rounded-full">
                  {currentLanguage === 'ar' ? 'نشط' : 'Active'}
                </span>
                <Button variant="ghost" size="sm" iconName="Edit">
                  {currentLanguage === 'ar' ? 'تعديل' : 'Edit'}
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-background border border-border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Icon name="Calendar" size={16} className="text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium text-text-primary">
                    {currentLanguage === 'ar' ? 'تقرير المخزون الشهري' : 'Monthly Inventory Report'}
                  </p>
                  <p className="text-xs text-text-secondary">
                    {currentLanguage === 'ar' ? 'اليوم الأول من كل شهر الساعة 8:00 صباحاً - PDF' : 'First day of each month at 8:00 AM - PDF'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <span className="px-2 py-1 bg-primary-100 text-primary text-xs rounded-full">
                  {currentLanguage === 'ar' ? 'نشط' : 'Active'}
                </span>
                <Button variant="ghost" size="sm" iconName="Edit">
                  {currentLanguage === 'ar' ? 'تعديل' : 'Edit'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Schedule Modal */}
      {isScheduleModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-surface rounded-lg shadow-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-text-primary">
                  {currentLanguage === 'ar' ? 'جدولة تقرير' : 'Schedule Report'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  iconName="X"
                  onClick={() => setIsScheduleModalOpen(false)}
                />
              </div>
            </div>

            <form onSubmit={handleScheduleSubmit} className="p-6 space-y-4">
              {/* Frequency */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {currentLanguage === 'ar' ? 'التكرار' : 'Frequency'}
                </label>
                <select
                  value={scheduleSettings.frequency}
                  onChange={(e) => setScheduleSettings(prev => ({ ...prev, frequency: e.target.value }))}
                  className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-text-primary"
                >
                  {scheduleFrequencies.map((freq) => (
                    <option key={freq.value} value={freq.value}>
                      {freq.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Day (for weekly/monthly) */}
              {(scheduleSettings.frequency === 'weekly' || scheduleSettings.frequency === 'monthly') && (
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    {currentLanguage === 'ar' ? 'اليوم' : 'Day'}
                  </label>
                  <select
                    value={scheduleSettings.day}
                    onChange={(e) => setScheduleSettings(prev => ({ ...prev, day: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-text-primary"
                  >
                    {weekDays.map((day) => (
                      <option key={day.value} value={day.value}>
                        {day.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Time */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {currentLanguage === 'ar' ? 'الوقت' : 'Time'}
                </label>
                <input
                  type="time"
                  value={scheduleSettings.time}
                  onChange={(e) => setScheduleSettings(prev => ({ ...prev, time: e.target.value }))}
                  className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-text-primary"
                />
              </div>

              {/* Format */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {currentLanguage === 'ar' ? 'التنسيق' : 'Format'}
                </label>
                <select
                  value={scheduleSettings.format}
                  onChange={(e) => setScheduleSettings(prev => ({ ...prev, format: e.target.value }))}
                  className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-text-primary"
                >
                  {exportFormats.map((format) => (
                    <option key={format.id} value={format.id}>
                      {format.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Recipients */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {currentLanguage === 'ar' ? 'المستلمون' : 'Recipients'}
                </label>
                <div className="space-y-2">
                  {scheduleSettings.recipients.map((recipient, index) => (
                    <div key={index} className="flex space-x-2 rtl:space-x-reverse">
                      <input
                        type="email"
                        value={recipient}
                        onChange={(e) => updateRecipient(index, e.target.value)}
                        placeholder={currentLanguage === 'ar' ? 'البريد الإلكتروني' : 'Email address'}
                        className="flex-1 px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-text-primary"
                      />
                      {scheduleSettings.recipients.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          iconName="X"
                          onClick={() => removeRecipient(index)}
                        />
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    iconName="Plus"
                    onClick={addRecipient}
                  >
                    {currentLanguage === 'ar' ? 'إضافة مستلم' : 'Add Recipient'}
                  </Button>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsScheduleModalOpen(false)}
                >
                  {currentLanguage === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button type="submit" variant="primary">
                  {currentLanguage === 'ar' ? 'جدولة التقرير' : 'Schedule Report'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportPanel;