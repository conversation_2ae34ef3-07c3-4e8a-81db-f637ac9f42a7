import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const HeaderNavigation = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);
  }, []);

  const navigationItems = [
    {
      label: currentLanguage === 'ar' ? 'لوحة التحكم' : 'Dashboard',
      path: '/dashboard',
      icon: 'LayoutDashboard',
      tooltip: currentLanguage === 'ar' ? 'مركز القيادة المركزي' : 'Central command center'
    },
    {
      label: currentLanguage === 'ar' ? 'العمليات' : 'Operations',
      path: '/operations',
      icon: 'Package',
      tooltip: currentLanguage === 'ar' ? 'سير العمل المركز على المعاملات' : 'Transaction-focused workflows',
      children: [
        {
          label: currentLanguage === 'ar' ? 'تتبع حركة المنتجات' : 'Product Movement Tracking',
          path: '/product-movement-tracking',
          icon: 'TruckIcon'
        },
        {
          label: currentLanguage === 'ar' ? 'التحويلات بين المستودعات' : 'Inter-Warehouse Transfers',
          path: '/inter-warehouse-transfers',
          icon: 'ArrowRightLeft'
        }
      ]
    },
    {
      label: currentLanguage === 'ar' ? 'المستودعات' : 'Warehouses',
      path: '/warehouse-management',
      icon: 'Building2',
      tooltip: currentLanguage === 'ar' ? 'إدارة المرافق المحددة' : 'Facility-specific management'
    },
    {
      label: currentLanguage === 'ar' ? 'التحليلات' : 'Analytics',
      path: '/analytics-dashboard',
      icon: 'BarChart3',
      tooltip: currentLanguage === 'ar' ? 'رؤى استراتيجية وتحليل الأداء' : 'Strategic insights and performance analysis'
    }
  ];

  const isActiveRoute = (path) => {
    if (path === '/operations') {
      return location.pathname === '/product-movement-tracking' || location.pathname === '/inter-warehouse-transfers';
    }
    return location.pathname === path;
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleLanguageToggle = () => {
    const newLanguage = currentLanguage === 'en' ? 'ar' : 'en';
    setCurrentLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
    window.dispatchEvent(new CustomEvent('languageChange', { detail: newLanguage }));
  };

  const Logo = () => (
    <Link to="/dashboard" className="flex items-center space-x-3 rtl:space-x-reverse">
      <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-600 rounded-lg flex items-center justify-center">
        <Icon name="Package" size={20} color="white" />
      </div>
      <span className="text-xl font-semibold text-text-primary font-heading">
        {currentLanguage === 'ar' ? 'مدير المستودعات' : 'Warehouse Manager'}
      </span>
    </Link>
  );

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-surface border-b border-border shadow-elevation-1">
      <div className="max-w-full px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Logo />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
            {navigationItems.map((item) => (
              <div key={item.path} className="relative group">
                {item.children ? (
                  <div className="relative">
                    <button
                      className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                        isActiveRoute(item.path)
                          ? 'text-primary bg-primary-50' :'text-text-secondary hover:text-primary hover:bg-primary-50'
                      }`}
                      title={item.tooltip}
                    >
                      <Icon name={item.icon} size={16} />
                      <span>{item.label}</span>
                      <Icon name="ChevronDown" size={14} />
                    </button>
                    
                    {/* Dropdown Menu */}
                    <div className="absolute top-full left-0 mt-1 w-64 bg-surface border border-border rounded-lg shadow-elevation-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-60">
                      <div className="py-2">
                        {item.children.map((child) => (
                          <Link
                            key={child.path}
                            to={child.path}
                            className={`flex items-center space-x-3 rtl:space-x-reverse px-4 py-2 text-sm transition-colors duration-150 ${
                              location.pathname === child.path
                                ? 'text-primary bg-primary-50' :'text-text-secondary hover:text-primary hover:bg-primary-50'
                            }`}
                          >
                            <Icon name={child.icon} size={16} />
                            <span>{child.label}</span>
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                      isActiveRoute(item.path)
                        ? 'text-primary bg-primary-50' :'text-text-secondary hover:text-primary hover:bg-primary-50'
                    }`}
                    title={item.tooltip}
                  >
                    <Icon name={item.icon} size={16} />
                    <span>{item.label}</span>
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Language Toggle */}
            <Button
              variant="ghost"
              onClick={handleLanguageToggle}
              className="p-2"
              title={currentLanguage === 'ar' ? 'Switch to English' : 'التبديل إلى العربية'}
            >
              <Icon name="Languages" size={18} />
            </Button>

            {/* User Menu */}
            <div className="relative group">
              <Button
                variant="ghost"
                className="flex items-center space-x-2 rtl:space-x-reverse p-2"
              >
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <Icon name="User" size={16} color="var(--color-primary)" />
                </div>
              </Button>
              
              {/* User Dropdown */}
              <div className="absolute top-full right-0 mt-1 w-48 bg-surface border border-border rounded-lg shadow-elevation-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-60">
                <div className="py-2">
                  <button className="flex items-center space-x-3 rtl:space-x-reverse w-full px-4 py-2 text-sm text-text-secondary hover:text-primary hover:bg-primary-50 transition-colors duration-150">
                    <Icon name="Settings" size={16} />
                    <span>{currentLanguage === 'ar' ? 'الإعدادات' : 'Settings'}</span>
                  </button>
                  <button className="flex items-center space-x-3 rtl:space-x-reverse w-full px-4 py-2 text-sm text-text-secondary hover:text-primary hover:bg-primary-50 transition-colors duration-150">
                    <Icon name="LogOut" size={16} />
                    <span>{currentLanguage === 'ar' ? 'تسجيل الخروج' : 'Logout'}</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              onClick={toggleMobileMenu}
              className="md:hidden p-2"
              aria-label="Toggle mobile menu"
            >
              <Icon name={isMobileMenuOpen ? "X" : "Menu"} size={20} />
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-surface border-t border-border shadow-elevation-2">
          <div className="px-4 py-4 space-y-2">
            {navigationItems.map((item) => (
              <div key={item.path}>
                {item.children ? (
                  <div>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 text-sm font-medium text-text-secondary">
                      <Icon name={item.icon} size={16} />
                      <span>{item.label}</span>
                    </div>
                    <div className="ml-6 rtl:mr-6 space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.path}
                          to={child.path}
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={`flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 rounded-md text-sm transition-colors duration-150 ${
                            location.pathname === child.path
                              ? 'text-primary bg-primary-50' :'text-text-secondary hover:text-primary hover:bg-primary-50'
                          }`}
                        >
                          <Icon name={child.icon} size={16} />
                          <span>{child.label}</span>
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors duration-150 ${
                      isActiveRoute(item.path)
                        ? 'text-primary bg-primary-50' :'text-text-secondary hover:text-primary hover:bg-primary-50'
                    }`}
                  >
                    <Icon name={item.icon} size={16} />
                    <span>{item.label}</span>
                  </Link>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default HeaderNavigation;