@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Colors */
    --color-primary: #1E40AF; /* blue-800 */
    --color-primary-50: #EFF6FF; /* blue-50 */
    --color-primary-100: #DBEAFE; /* blue-100 */
    --color-primary-200: #BFDBFE; /* blue-200 */
    --color-primary-500: #3B82F6; /* blue-500 */
    --color-primary-600: #2563EB; /* blue-600 */
    --color-primary-700: #1D4ED8; /* blue-700 */
    --color-primary-900: #1E3A8A; /* blue-900 */
    --color-primary-foreground: #FFFFFF; /* white */

    /* Secondary Colors */
    --color-secondary: #64748B; /* slate-500 */
    --color-secondary-50: #F8FAFC; /* slate-50 */
    --color-secondary-100: #F1F5F9; /* slate-100 */
    --color-secondary-200: #E2E8F0; /* slate-200 */
    --color-secondary-300: #CBD5E1; /* slate-300 */
    --color-secondary-400: #94A3B8; /* slate-400 */
    --color-secondary-600: #475569; /* slate-600 */
    --color-secondary-700: #334155; /* slate-700 */
    --color-secondary-800: #1E293B; /* slate-800 */
    --color-secondary-900: #0F172A; /* slate-900 */
    --color-secondary-foreground: #FFFFFF; /* white */

    /* Accent Colors */
    --color-accent: #F59E0B; /* amber-500 */
    --color-accent-50: #FFFBEB; /* amber-50 */
    --color-accent-100: #FEF3C7; /* amber-100 */
    --color-accent-200: #FDE68A; /* amber-200 */
    --color-accent-300: #FCD34D; /* amber-300 */
    --color-accent-400: #FBBF24; /* amber-400 */
    --color-accent-600: #D97706; /* amber-600 */
    --color-accent-700: #B45309; /* amber-700 */
    --color-accent-800: #92400E; /* amber-800 */
    --color-accent-900: #78350F; /* amber-900 */
    --color-accent-foreground: #FFFFFF; /* white */

    /* Background Colors */
    --color-background: #F8FAFC; /* slate-50 */
    --color-background-secondary: #F1F5F9; /* slate-100 */
    --color-background-tertiary: #E2E8F0; /* slate-200 */

    /* Surface Colors */
    --color-surface: #FFFFFF; /* white */
    --color-surface-secondary: #F8FAFC; /* slate-50 */
    --color-surface-tertiary: #F1F5F9; /* slate-100 */

    /* Text Colors */
    --color-text-primary: #0F172A; /* slate-900 */
    --color-text-secondary: #475569; /* slate-600 */
    --color-text-tertiary: #64748B; /* slate-500 */
    --color-text-muted: #94A3B8; /* slate-400 */
    --color-text-inverse: #FFFFFF; /* white */

    /* Status Colors */
    --color-success: #059669; /* emerald-600 */
    --color-success-50: #ECFDF5; /* emerald-50 */
    --color-success-100: #D1FAE5; /* emerald-100 */
    --color-success-200: #A7F3D0; /* emerald-200 */
    --color-success-500: #10B981; /* emerald-500 */
    --color-success-700: #047857; /* emerald-700 */
    --color-success-foreground: #FFFFFF; /* white */

    --color-warning: #D97706; /* amber-600 */
    --color-warning-50: #FFFBEB; /* amber-50 */
    --color-warning-100: #FEF3C7; /* amber-100 */
    --color-warning-200: #FDE68A; /* amber-200 */
    --color-warning-500: #F59E0B; /* amber-500 */
    --color-warning-700: #B45309; /* amber-700 */
    --color-warning-foreground: #FFFFFF; /* white */

    --color-error: #DC2626; /* red-600 */
    --color-error-50: #FEF2F2; /* red-50 */
    --color-error-100: #FEE2E2; /* red-100 */
    --color-error-200: #FECACA; /* red-200 */
    --color-error-500: #EF4444; /* red-500 */
    --color-error-700: #B91C1C; /* red-700 */
    --color-error-foreground: #FFFFFF; /* white */

    /* Border Colors */
    --color-border: rgba(148, 163, 184, 0.2); /* slate-400 with opacity */
    --color-border-light: rgba(203, 213, 225, 0.3); /* slate-300 with opacity */
    --color-border-strong: #CBD5E1; /* slate-300 */

    /* Shadow Colors */
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-strong: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Typography */
    --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-caption: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-data: 'JetBrains Mono', 'Courier New', monospace;

    /* Spacing */
    --spacing-xs: 0.25rem; /* 4px */
    --spacing-sm: 0.5rem; /* 8px */
    --spacing-md: 1rem; /* 16px */
    --spacing-lg: 1.5rem; /* 24px */
    --spacing-xl: 2rem; /* 32px */
    --spacing-2xl: 3rem; /* 48px */
    --spacing-3xl: 4rem; /* 64px */

    /* Border Radius */
    --radius-sm: 0.25rem; /* 4px */
    --radius-md: 0.375rem; /* 6px */
    --radius-lg: 0.5rem; /* 8px */
    --radius-xl: 0.75rem; /* 12px */

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 200ms ease-out;
    --transition-slow: 300ms ease-in-out;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-text-primary font-body;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }

  .font-data {
    font-family: var(--font-data);
  }
}

@layer components {
  .card {
    @apply bg-surface border border-border rounded-lg shadow-sm;
  }

  .card-header {
    @apply p-6 pb-4;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply p-6 pt-4;
  }

  .btn-transition {
    @apply transition-all duration-200 ease-out;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary to-primary-600 bg-clip-text text-transparent;
  }

  .shadow-elevation-1 {
    box-shadow: var(--shadow-light);
  }

  .shadow-elevation-2 {
    box-shadow: var(--shadow-medium);
  }

  .shadow-elevation-3 {
    box-shadow: var(--shadow-strong);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animation-delay-100 {
    animation-delay: 100ms;
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-300 {
    animation-delay: 300ms;
  }
}