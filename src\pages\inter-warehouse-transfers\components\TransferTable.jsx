import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import TransferDetailsModal from './TransferDetailsModal';

const TransferTable = ({ transfers, onStatusUpdate, currentLanguage }) => {
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [selectedTransfer, setSelectedTransfer] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const toggleRowExpansion = (transferId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(transferId)) {
      newExpanded.delete(transferId);
    } else {
      newExpanded.add(transferId);
    }
    setExpandedRows(newExpanded);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-warning-100 text-warning-700';
      case 'approved':
        return 'bg-primary-100 text-primary-700';
      case 'in-transit':
        return 'bg-accent-100 text-accent-700';
      case 'completed':
        return 'bg-success-100 text-success-700';
      case 'cancelled':
        return 'bg-error-100 text-error-700';
      default:
        return 'bg-secondary-100 text-secondary-700';
    }
  };

  const getStatusText = (status) => {
    const statusMap = {
      'pending': currentLanguage === 'ar' ? 'معلق' : 'Pending',
      'approved': currentLanguage === 'ar' ? 'موافق عليه' : 'Approved',
      'in-transit': currentLanguage === 'ar' ? 'في الطريق' : 'In Transit',
      'completed': currentLanguage === 'ar' ? 'مكتمل' : 'Completed',
      'cancelled': currentLanguage === 'ar' ? 'ملغي' : 'Cancelled'
    };
    return statusMap[status] || status;
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'text-error';
      case 'high':
        return 'text-warning';
      case 'normal':
        return 'text-primary';
      case 'low':
        return 'text-text-muted';
      default:
        return 'text-text-secondary';
    }
  };

  const getPriorityText = (priority) => {
    const priorityMap = {
      'urgent': currentLanguage === 'ar' ? 'عاجل' : 'Urgent',
      'high': currentLanguage === 'ar' ? 'عالي' : 'High',
      'normal': currentLanguage === 'ar' ? 'عادي' : 'Normal',
      'low': currentLanguage === 'ar' ? 'منخفض' : 'Low'
    };
    return priorityMap[priority] || priority;
  };

  const handleViewDetails = (transfer) => {
    setSelectedTransfer(transfer);
    setShowDetailsModal(true);
  };

  return (
    <>
      <div className="bg-surface border border-border rounded-lg overflow-hidden">
        {/* Desktop Table */}
        <div className="hidden lg:block overflow-x-auto">
          <table className="w-full">
            <thead className="bg-background-secondary border-b border-border">
              <tr>
                <th className="px-6 py-4 text-left rtl:text-right text-sm font-medium text-text-secondary">
                  {currentLanguage === 'ar' ? 'معرف التحويل' : 'Transfer ID'}
                </th>
                <th className="px-6 py-4 text-left rtl:text-right text-sm font-medium text-text-secondary">
                  {currentLanguage === 'ar' ? 'المسار' : 'Route'}
                </th>
                <th className="px-6 py-4 text-left rtl:text-right text-sm font-medium text-text-secondary">
                  {currentLanguage === 'ar' ? 'المنتجات' : 'Products'}
                </th>
                <th className="px-6 py-4 text-left rtl:text-right text-sm font-medium text-text-secondary">
                  {currentLanguage === 'ar' ? 'القيمة' : 'Value'}
                </th>
                <th className="px-6 py-4 text-left rtl:text-right text-sm font-medium text-text-secondary">
                  {currentLanguage === 'ar' ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-4 text-left rtl:text-right text-sm font-medium text-text-secondary">
                  {currentLanguage === 'ar' ? 'الأولوية' : 'Priority'}
                </th>
                <th className="px-6 py-4 text-left rtl:text-right text-sm font-medium text-text-secondary">
                  {currentLanguage === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {transfers.map((transfer) => (
                <React.Fragment key={transfer.id}>
                  <tr className="hover:bg-background-secondary transition-colors duration-150">
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Button
                          variant="ghost"
                          onClick={() => toggleRowExpansion(transfer.id)}
                          className="p-1"
                        >
                          <Icon 
                            name={expandedRows.has(transfer.id) ? "ChevronDown" : "ChevronRight"} 
                            size={16} 
                          />
                        </Button>
                        <span className="font-medium text-text-primary">{transfer.id}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="text-sm">
                          <div className="font-medium text-text-primary">{transfer.originWarehouse}</div>
                          <div className="text-text-muted flex items-center space-x-1 rtl:space-x-reverse">
                            <Icon name="ArrowRight" size={12} className="rtl:rotate-180" />
                            <span>{transfer.destinationWarehouse}</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        <div className="font-medium text-text-primary">
                          {transfer.products.length} {currentLanguage === 'ar' ? 'منتج' : 'items'}
                        </div>
                        <div className="text-text-muted">
                          {transfer.totalQuantity} {currentLanguage === 'ar' ? 'وحدة' : 'units'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-text-primary">
                        {transfer.totalValue.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transfer.status)}`}>
                        {getStatusText(transfer.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`text-sm font-medium ${getPriorityColor(transfer.priority)}`}>
                        {getPriorityText(transfer.priority)}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Button
                          variant="outline"
                          onClick={() => handleViewDetails(transfer)}
                          iconName="Eye"
                          className="p-2"
                        />
                        {transfer.status === 'pending' && (
                          <Button
                            variant="primary"
                            onClick={() => onStatusUpdate(transfer.id, 'approved')}
                            iconName="Check"
                            className="p-2"
                          />
                        )}
                      </div>
                    </td>
                  </tr>
                  
                  {/* Expanded Row Details */}
                  {expandedRows.has(transfer.id) && (
                    <tr>
                      <td colSpan="7" className="px-6 py-4 bg-background-secondary">
                        <div className="space-y-4">
                          {/* Transfer Progress */}
                          <div>
                            <h4 className="text-sm font-medium text-text-primary mb-2">
                              {currentLanguage === 'ar' ? 'تقدم التحويل' : 'Transfer Progress'}
                            </h4>
                            <div className="flex items-center space-x-4 rtl:space-x-reverse">
                              {['requested', 'approved', 'picked', 'in-transit', 'delivered'].map((step, index) => (
                                <div key={step} className="flex items-center">
                                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                    transfer.progress >= index + 1 ? 'bg-primary text-white' : 'bg-secondary-200 text-text-muted'
                                  }`}>
                                    <Icon name="Check" size={14} />
                                  </div>
                                  {index < 4 && (
                                    <div className={`w-12 h-0.5 ${
                                      transfer.progress > index + 1 ? 'bg-primary' : 'bg-secondary-200'
                                    }`} />
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Product Details */}
                          <div>
                            <h4 className="text-sm font-medium text-text-primary mb-2">
                              {currentLanguage === 'ar' ? 'تفاصيل المنتجات' : 'Product Details'}
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {transfer.products.slice(0, 4).map((product) => (
                                <div key={product.id} className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-surface rounded-lg">
                                  <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                    <Icon name="Package" size={16} color="var(--color-primary)" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="text-sm font-medium text-text-primary">{product.name}</div>
                                    <div className="text-xs text-text-muted">
                                      {product.quantity} {currentLanguage === 'ar' ? 'وحدة' : 'units'} • {product.value.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Tracking Information */}
                          {transfer.status === 'in-transit' && transfer.tracking && (
                            <div>
                              <h4 className="text-sm font-medium text-text-primary mb-2">
                                {currentLanguage === 'ar' ? 'معلومات التتبع' : 'Tracking Information'}
                              </h4>
                              <div className="bg-surface p-3 rounded-lg">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                    <Icon name="MapPin" size={16} color="var(--color-primary)" />
                                    <span className="text-sm text-text-primary">{transfer.tracking.currentLocation}</span>
                                  </div>
                                  <div className="text-sm text-text-muted">
                                    {currentLanguage === 'ar' ? 'الوصول المتوقع:' : 'ETA:'} {transfer.tracking.estimatedArrival}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Cards */}
        <div className="lg:hidden divide-y divide-border">
          {transfers.map((transfer) => (
            <div key={transfer.id} className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <div className="font-medium text-text-primary">{transfer.id}</div>
                  <div className="text-sm text-text-muted">{transfer.requestedDate}</div>
                </div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transfer.status)}`}>
                  {getStatusText(transfer.status)}
                </span>
              </div>
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                  <Icon name="ArrowRight" size={14} className="text-text-muted rtl:rotate-180" />
                  <span className="text-text-primary">{transfer.originWarehouse}</span>
                  <Icon name="ArrowRight" size={14} className="text-text-muted rtl:rotate-180" />
                  <span className="text-text-primary">{transfer.destinationWarehouse}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-text-muted">
                    {transfer.products.length} {currentLanguage === 'ar' ? 'منتج' : 'items'} • {transfer.totalQuantity} {currentLanguage === 'ar' ? 'وحدة' : 'units'}
                  </span>
                  <span className="font-medium text-text-primary">
                    {transfer.totalValue.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className={`text-sm font-medium ${getPriorityColor(transfer.priority)}`}>
                  {getPriorityText(transfer.priority)}
                </span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Button
                    variant="outline"
                    onClick={() => handleViewDetails(transfer)}
                    iconName="Eye"
                    className="p-2"
                  />
                  {transfer.status === 'pending' && (
                    <Button
                      variant="primary"
                      onClick={() => onStatusUpdate(transfer.id, 'approved')}
                      iconName="Check"
                      className="p-2"
                    />
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Transfer Details Modal */}
      {showDetailsModal && selectedTransfer && (
        <TransferDetailsModal
          transfer={selectedTransfer}
          onClose={() => setShowDetailsModal(false)}
          currentLanguage={currentLanguage}
        />
      )}
    </>
  );
};

export default TransferTable;