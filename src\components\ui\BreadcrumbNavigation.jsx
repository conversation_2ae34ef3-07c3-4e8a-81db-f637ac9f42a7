import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../AppIcon';

const BreadcrumbNavigation = ({ warehouseContext, className = "" }) => {
  const location = useLocation();
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const routeLabels = {
    '/dashboard': currentLanguage === 'ar' ? 'لوحة التحكم' : 'Dashboard',
    '/warehouse-management': currentLanguage === 'ar' ? 'إدارة المستودعات' : 'Warehouse Management',
    '/product-movement-tracking': currentLanguage === 'ar' ? 'تتبع حركة المنتجات' : 'Product Movement Tracking',
    '/inter-warehouse-transfers': currentLanguage === 'ar' ? 'التحويلات بين المستودعات' : 'Inter-Warehouse Transfers',
    '/analytics-dashboard': currentLanguage === 'ar' ? 'لوحة التحليلات' : 'Analytics Dashboard',
    '/login': currentLanguage === 'ar' ? 'تسجيل الدخول' : 'Login'
  };

  const generateBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(segment => segment);
    const breadcrumbs = [];

    // Always start with home/dashboard
    breadcrumbs.push({
      label: currentLanguage === 'ar' ? 'الرئيسية' : 'Home',
      path: '/dashboard',
      icon: 'Home',
      isHome: true
    });

    // Skip if we're already on dashboard
    if (location.pathname === '/dashboard') {
      return breadcrumbs;
    }

    // Build breadcrumb path
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Get label for this segment
      const label = routeLabels[currentPath] || segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      
      // Determine if this is the last segment (current page)
      const isLast = index === pathSegments.length - 1;
      
      breadcrumbs.push({
        label,
        path: currentPath,
        icon: getIconForPath(currentPath),
        isLast
      });
    });

    return breadcrumbs;
  };

  const getIconForPath = (path) => {
    switch (path) {
      case '/dashboard':
        return 'LayoutDashboard';
      case '/warehouse-management':
        return 'Building2';
      case '/product-movement-tracking':
        return 'TruckIcon';
      case '/inter-warehouse-transfers':
        return 'ArrowRightLeft';
      case '/analytics-dashboard':
        return 'BarChart3';
      default:
        return 'Folder';
    }
  };

  const breadcrumbs = generateBreadcrumbs();

  // Don't render breadcrumbs on login page or if only home breadcrumb
  if (location.pathname === '/login' || breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <nav 
      className={`flex items-center space-x-2 rtl:space-x-reverse text-sm ${className}`}
      aria-label="Breadcrumb"
    >
      {/* Warehouse Context */}
      {warehouseContext && (
        <>
          <div className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-1.5 bg-primary-50 rounded-md">
            <Icon 
              name={warehouseContext.type === 'main' ? 'Building2' : 'Warehouse'} 
              size={14} 
              color="var(--color-primary)" 
            />
            <span className="text-primary font-medium text-xs">
              {warehouseContext.name}
            </span>
          </div>
          <Icon name="ChevronRight" size={14} className="text-text-muted rtl:rotate-180" />
        </>
      )}

      {/* Breadcrumb Items */}
      <ol className="flex items-center space-x-2 rtl:space-x-reverse">
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.path} className="flex items-center">
            {index > 0 && (
              <Icon 
                name="ChevronRight" 
                size={14} 
                className="text-text-muted mx-2 rtl:rotate-180" 
              />
            )}
            
            {breadcrumb.isLast ? (
              <span className="flex items-center space-x-1.5 rtl:space-x-reverse text-text-primary font-medium">
                <Icon name={breadcrumb.icon} size={14} />
                <span>{breadcrumb.label}</span>
              </span>
            ) : (
              <Link
                to={breadcrumb.path}
                className="flex items-center space-x-1.5 rtl:space-x-reverse text-text-secondary hover:text-primary transition-colors duration-150"
              >
                <Icon name={breadcrumb.icon} size={14} />
                <span>{breadcrumb.label}</span>
              </Link>
            )}
          </li>
        ))}
      </ol>

      {/* Mobile Breadcrumb (Truncated) */}
      <div className="sm:hidden flex items-center space-x-2 rtl:space-x-reverse">
        {breadcrumbs.length > 2 && (
          <>
            <Link
              to={breadcrumbs[0].path}
              className="flex items-center space-x-1 rtl:space-x-reverse text-text-secondary hover:text-primary transition-colors duration-150"
            >
              <Icon name={breadcrumbs[0].icon} size={14} />
              <span className="sr-only">{breadcrumbs[0].label}</span>
            </Link>
            <Icon name="MoreHorizontal" size={14} className="text-text-muted" />
            <Icon name="ChevronRight" size={14} className="text-text-muted rtl:rotate-180" />
          </>
        )}
        <span className="flex items-center space-x-1.5 rtl:space-x-reverse text-text-primary font-medium">
          <Icon name={breadcrumbs[breadcrumbs.length - 1].icon} size={14} />
          <span>{breadcrumbs[breadcrumbs.length - 1].label}</span>
        </span>
      </div>
    </nav>
  );
};

export default BreadcrumbNavigation;