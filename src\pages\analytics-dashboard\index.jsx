import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import HeaderNavigation from '../../components/ui/HeaderNavigation';
import ContextualSidebar from '../../components/ui/ContextualSidebar';
import BreadcrumbNavigation from '../../components/ui/BreadcrumbNavigation';
import KPICard from './components/KPICard';
import WarehouseCapacityHeatMap from './components/WarehouseCapacityHeatMap';
import ProductMovementFlow from './components/ProductMovementFlow';
import InventoryTurnoverChart from './components/InventoryTurnoverChart';
import SeasonalTrendAnalysis from './components/SeasonalTrendAnalysis';
import PerformanceTable from './components/PerformanceTable';
import FilterControls from './components/FilterControls';

import Button from '../../components/ui/Button';

const AnalyticsDashboard = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [filters, setFilters] = useState({});

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  // Mock data for KPI cards
  const kpiData = [
    {
      title: currentLanguage === 'ar' ? 'إجمالي قيمة المخزون' : 'Total Inventory Value',
      value: '$2.4M',
      trend: '+12.5%',
      trendDirection: 'up',
      icon: 'DollarSign',
      color: 'primary'
    },
    {
      title: currentLanguage === 'ar' ? 'سرعة الحركة' : 'Movement Velocity',
      value: '1,847',
      unit: currentLanguage === 'ar' ? 'وحدة/يوم' : 'units/day',
      trend: '+8.3%',
      trendDirection: 'up',
      icon: 'TruckIcon',
      color: 'success'
    },
    {
      title: currentLanguage === 'ar' ? 'استخدام السعة' : 'Capacity Utilization',
      value: '78.2',
      unit: '%',
      trend: '+2.1%',
      trendDirection: 'up',
      icon: 'BarChart3',
      color: 'warning'
    },
    {
      title: currentLanguage === 'ar' ? 'كفاءة التكلفة' : 'Cost Efficiency',
      value: '$1.23',
      unit: currentLanguage === 'ar' ? '/وحدة' : '/unit',
      trend: '-5.2%',
      trendDirection: 'down',
      icon: 'TrendingDown',
      color: 'error'
    }
  ];

  // Mock data for warehouse capacity heat map
  const capacityData = [
    {
      id: 'wh-001',
      name: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      shortName: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh',
      capacity: 85,
      products: 2847,
      governorate: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh'
    },
    {
      id: 'wh-002',
      name: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
      shortName: currentLanguage === 'ar' ? 'الدمام' : 'Dammam',
      capacity: 72,
      products: 1923,
      governorate: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province'
    },
    {
      id: 'wh-003',
      name: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
      shortName: currentLanguage === 'ar' ? 'جدة' : 'Jeddah',
      capacity: 68,
      products: 1756,
      governorate: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah'
    },
    {
      id: 'wh-004',
      name: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk',
      shortName: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk',
      capacity: 45,
      products: 892,
      governorate: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk'
    },
    {
      id: 'wh-005',
      name: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
      shortName: currentLanguage === 'ar' ? 'أبها' : 'Abha',
      capacity: 58,
      products: 1234,
      governorate: currentLanguage === 'ar' ? 'عسير' : 'Asir'
    }
  ];

  // Mock data for product movement flow
  const movementFlowData = [
    {
      date: currentLanguage === 'ar' ? '1 يناير' : 'Jan 1',
      inbound: 245,
      outbound: 189,
      transfers: 34
    },
    {
      date: currentLanguage === 'ar' ? '2 يناير' : 'Jan 2',
      inbound: 267,
      outbound: 203,
      transfers: 28
    },
    {
      date: currentLanguage === 'ar' ? '3 يناير' : 'Jan 3',
      inbound: 198,
      outbound: 234,
      transfers: 41
    },
    {
      date: currentLanguage === 'ar' ? '4 يناير' : 'Jan 4',
      inbound: 289,
      outbound: 178,
      transfers: 37
    },
    {
      date: currentLanguage === 'ar' ? '5 يناير' : 'Jan 5',
      inbound: 234,
      outbound: 256,
      transfers: 29
    },
    {
      date: currentLanguage === 'ar' ? '6 يناير' : 'Jan 6',
      inbound: 312,
      outbound: 198,
      transfers: 45
    },
    {
      date: currentLanguage === 'ar' ? '7 يناير' : 'Jan 7',
      inbound: 278,
      outbound: 223,
      transfers: 38
    }
  ];

  // Mock data for inventory turnover
  const turnoverData = [
    {
      category: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics',
      value: 450000,
      turnoverRate: 8.5
    },
    {
      category: currentLanguage === 'ar' ? 'ملابس' : 'Clothing',
      value: 320000,
      turnoverRate: 6.2
    },
    {
      category: currentLanguage === 'ar' ? 'طعام ومشروبات' : 'Food & Beverages',
      value: 280000,
      turnoverRate: 12.3
    },
    {
      category: currentLanguage === 'ar' ? 'سيارات' : 'Automotive',
      value: 180000,
      turnoverRate: 4.1
    },
    {
      category: currentLanguage === 'ar' ? 'منزلية' : 'Home & Garden',
      value: 150000,
      turnoverRate: 5.8
    },
    {
      category: currentLanguage === 'ar' ? 'أخرى' : 'Others',
      value: 120000,
      turnoverRate: 3.9
    }
  ];

  // Mock data for seasonal trends
  const seasonalData = [
    {
      month: currentLanguage === 'ar' ? 'يناير' : 'Jan',
      currentYear: 2400000,
      previousYear: 2200000
    },
    {
      month: currentLanguage === 'ar' ? 'فبراير' : 'Feb',
      currentYear: 2300000,
      previousYear: 2100000
    },
    {
      month: currentLanguage === 'ar' ? 'مارس' : 'Mar',
      currentYear: 2100000,
      previousYear: 1950000
    },
    {
      month: currentLanguage === 'ar' ? 'أبريل' : 'Apr',
      currentYear: 1900000,
      previousYear: 1800000
    },
    {
      month: currentLanguage === 'ar' ? 'مايو' : 'May',
      currentYear: 1800000,
      previousYear: 1700000
    },
    {
      month: currentLanguage === 'ar' ? 'يونيو' : 'Jun',
      currentYear: 1600000,
      previousYear: 1500000
    },
    {
      month: currentLanguage === 'ar' ? 'يوليو' : 'Jul',
      currentYear: 1500000,
      previousYear: 1400000
    },
    {
      month: currentLanguage === 'ar' ? 'أغسطس' : 'Aug',
      currentYear: 1550000,
      previousYear: 1450000
    },
    {
      month: currentLanguage === 'ar' ? 'سبتمبر' : 'Sep',
      currentYear: 1750000,
      previousYear: 1650000
    },
    {
      month: currentLanguage === 'ar' ? 'أكتوبر' : 'Oct',
      currentYear: 2000000,
      previousYear: 1900000
    },
    {
      month: currentLanguage === 'ar' ? 'نوفمبر' : 'Nov',
      currentYear: 2200000,
      previousYear: 2050000
    },
    {
      month: currentLanguage === 'ar' ? 'ديسمبر' : 'Dec',
      currentYear: 2500000,
      previousYear: 2300000
    }
  ];

  // Mock data for performance table
  const performanceData = [
    {
      id: 'wh-001',
      name: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      location: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh',
      efficiency: 94,
      throughput: 1847,
      costEfficiency: 1.23,
      accuracy: 98.5
    },
    {
      id: 'wh-003',
      name: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
      location: currentLanguage === 'ar' ? 'جدة' : 'Jeddah',
      efficiency: 89,
      throughput: 1456,
      costEfficiency: 1.34,
      accuracy: 97.2
    },
    {
      id: 'wh-002',
      name: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
      location: currentLanguage === 'ar' ? 'الدمام' : 'Dammam',
      efficiency: 86,
      throughput: 1234,
      costEfficiency: 1.45,
      accuracy: 96.8
    },
    {
      id: 'wh-005',
      name: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
      location: currentLanguage === 'ar' ? 'أبها' : 'Abha',
      efficiency: 82,
      throughput: 987,
      costEfficiency: 1.56,
      accuracy: 95.4
    },
    {
      id: 'wh-004',
      name: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk',
      location: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk',
      efficiency: 78,
      throughput: 756,
      costEfficiency: 1.67,
      accuracy: 94.1
    }
  ];

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    // In a real app, this would trigger data refetch
    console.log('Filters changed:', newFilters);
  };

  return (
    <div className="min-h-screen bg-background">
      <Helmet>
        <title>
          {currentLanguage === 'ar' ? 'لوحة التحليلات - مدير المستودعات' : 'Analytics Dashboard - Warehouse Manager'}
        </title>
        <meta 
          name="description" 
          content={currentLanguage === 'ar' ?'لوحة تحليلات شاملة لأداء المستودعات وأنماط حركة المنتجات ومقاييس الكفاءة التشغيلية' :'Comprehensive analytics dashboard for warehouse performance, product movement patterns, and operational efficiency metrics'
          } 
        />
      </Helmet>

      <HeaderNavigation />
      
      <div className="flex">
        <ContextualSidebar 
          isOpen={isSidebarOpen} 
          onToggle={() => setIsSidebarOpen(!isSidebarOpen)} 
        />
        
        <main className="flex-1 lg:ml-0">
          <div className="p-6 pt-24 lg:pt-6">
            {/* Header Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <Button
                    variant="ghost"
                    onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                    className="lg:hidden"
                    iconName="Menu"
                  />
                  <div>
                    <h1 className="text-3xl font-bold text-text-primary">
                      {currentLanguage === 'ar' ? 'لوحة التحليلات' : 'Analytics Dashboard'}
                    </h1>
                    <p className="text-text-secondary mt-1">
                      {currentLanguage === 'ar' ?'رؤى شاملة حول أداء المستودعات وأنماط حركة المنتجات' :'Comprehensive insights into warehouse performance and product movement patterns'
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Button variant="outline" iconName="RefreshCw" iconPosition="left">
                    {currentLanguage === 'ar' ? 'تحديث' : 'Refresh'}
                  </Button>
                  <Button variant="primary" iconName="Download" iconPosition="left">
                    {currentLanguage === 'ar' ? 'تصدير التقرير' : 'Export Report'}
                  </Button>
                </div>
              </div>
              
              <BreadcrumbNavigation warehouseContext={selectedWarehouse} />
            </div>

            {/* Filter Controls */}
            <FilterControls 
              onFiltersChange={handleFiltersChange}
              currentLanguage={currentLanguage}
            />

            {/* KPI Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {kpiData.map((kpi, index) => (
                <KPICard
                  key={index}
                  title={kpi.title}
                  value={kpi.value}
                  unit={kpi.unit}
                  trend={kpi.trend}
                  trendDirection={kpi.trendDirection}
                  icon={kpi.icon}
                  color={kpi.color}
                />
              ))}
            </div>

            {/* Charts Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              <WarehouseCapacityHeatMap 
                data={capacityData}
                currentLanguage={currentLanguage}
              />
              <ProductMovementFlow 
                data={movementFlowData}
                currentLanguage={currentLanguage}
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              <InventoryTurnoverChart 
                data={turnoverData}
                currentLanguage={currentLanguage}
              />
              <SeasonalTrendAnalysis 
                data={seasonalData}
                currentLanguage={currentLanguage}
              />
            </div>

            {/* Performance Table */}
            <PerformanceTable 
              data={performanceData}
              currentLanguage={currentLanguage}
            />

            {/* Footer */}
            <div className="mt-8 pt-6 border-t border-border">
              <div className="flex items-center justify-between text-sm text-text-secondary">
                <p>
                  {currentLanguage === 'ar' 
                    ? `آخر تحديث: ${new Date().toLocaleDateString('ar-SA')}`
                    : `Last updated: ${new Date().toLocaleDateString()}`
                  }
                </p>
                <p>
                  © {new Date().getFullYear()} {currentLanguage === 'ar' ? 'مدير المستودعات' : 'Warehouse Manager'}
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;