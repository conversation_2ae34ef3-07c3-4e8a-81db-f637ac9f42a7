import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const LowStockAlerts = ({ currentLanguage }) => {
  const [alertFilter, setAlertFilter] = useState('all');

  // Mock low stock data
  const lowStockItems = [
    {
      id: 'PRD-001',
      name: currentLanguage === 'ar' ? 'جهاز كمبيوتر محمول HP' : 'HP Laptop Computer',
      category: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics',
      currentStock: 5,
      minThreshold: 20,
      warehouse: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh',
      severity: 'critical',
      lastRestocked: '2024-01-15',
      supplier: currentLanguage === 'ar' ? 'شركة التقنية المتقدمة' : 'Advanced Tech Co.'
    },
    {
      id: 'PRD-002',
      name: currentLanguage === 'ar' ? 'قميص قطني رجالي' : 'Men\'s Cotton Shirt',
      category: currentLanguage === 'ar' ? 'ملابس' : 'Clothing',
      currentStock: 12,
      minThreshold: 25,
      warehouse: currentLanguage === 'ar' ? 'جدة' : 'Jeddah',
      severity: 'warning',
      lastRestocked: '2024-01-18',
      supplier: currentLanguage === 'ar' ? 'مصنع الأزياء الحديثة' : 'Modern Fashion Factory'
    },
    {
      id: 'PRD-003',
      name: currentLanguage === 'ar' ? 'عصير برتقال طبيعي' : 'Natural Orange Juice',
      category: currentLanguage === 'ar' ? 'طعام ومشروبات' : 'Food & Beverages',
      currentStock: 3,
      minThreshold: 50,
      warehouse: currentLanguage === 'ar' ? 'الدمام' : 'Dammam',
      severity: 'critical',
      lastRestocked: '2024-01-20',
      supplier: currentLanguage === 'ar' ? 'شركة المشروبات الطازجة' : 'Fresh Beverages Co.'
    },
    {
      id: 'PRD-004',
      name: currentLanguage === 'ar' ? 'قطع غيار سيارات' : 'Car Spare Parts',
      category: currentLanguage === 'ar' ? 'سيارات' : 'Automotive',
      currentStock: 8,
      minThreshold: 15,
      warehouse: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh',
      severity: 'warning',
      lastRestocked: '2024-01-12',
      supplier: currentLanguage === 'ar' ? 'مركز قطع الغيار' : 'Auto Parts Center'
    },
    {
      id: 'PRD-005',
      name: currentLanguage === 'ar' ? 'أدوات حديقة' : 'Garden Tools',
      category: currentLanguage === 'ar' ? 'منزلية وحديقة' : 'Home & Garden',
      currentStock: 2,
      minThreshold: 10,
      warehouse: currentLanguage === 'ar' ? 'أبها' : 'Abha',
      severity: 'critical',
      lastRestocked: '2024-01-10',
      supplier: currentLanguage === 'ar' ? 'متجر الحديقة المثالية' : 'Perfect Garden Store'
    }
  ];

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'bg-error-100 text-error border-error-200';
      case 'warning':
        return 'bg-warning-100 text-warning border-warning-200';
      default:
        return 'bg-primary-100 text-primary border-primary-200';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return 'AlertTriangle';
      case 'warning':
        return 'AlertCircle';
      default:
        return 'Info';
    }
  };

  const getSeverityLabel = (severity) => {
    switch (severity) {
      case 'critical':
        return currentLanguage === 'ar' ? 'حرج' : 'Critical';
      case 'warning':
        return currentLanguage === 'ar' ? 'تحذير' : 'Warning';
      default:
        return currentLanguage === 'ar' ? 'عادي' : 'Normal';
    }
  };

  const filteredItems = alertFilter === 'all' 
    ? lowStockItems 
    : lowStockItems.filter(item => item.severity === alertFilter);

  const handleRestock = (itemId) => {
    console.log('Initiating restock for item:', itemId);
    // In a real application, this would trigger restock workflow
  };

  const handleContactSupplier = (itemId, supplier) => {
    console.log('Contacting supplier:', supplier, 'for item:', itemId);
    // In a real application, this would open supplier contact interface
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return currentLanguage === 'ar' ? date.toLocaleDateString('ar-SA')
      : date.toLocaleDateString('en-US');
  };

  return (
    <div className="bg-surface border border-border rounded-lg">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
              <Icon name="AlertTriangle" size={20} className="text-warning" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary">
                {currentLanguage === 'ar' ? 'تنبيهات المخزون المنخفض' : 'Low Stock Alerts'}
              </h3>
              <p className="text-sm text-text-secondary">
                {currentLanguage === 'ar' ? 'المنتجات التي تحتاج إلى إعادة تخزين' : 'Products that need restocking'}
              </p>
            </div>
          </div>
          
          {/* Filter Buttons */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="flex bg-background rounded-lg p-1">
              <button
                onClick={() => setAlertFilter('all')}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  alertFilter === 'all' ?'bg-primary text-white' :'text-text-secondary hover:text-text-primary'
                }`}
              >
                {currentLanguage === 'ar' ? 'الكل' : 'All'}
              </button>
              <button
                onClick={() => setAlertFilter('critical')}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  alertFilter === 'critical' ?'bg-error text-white' :'text-text-secondary hover:text-text-primary'
                }`}
              >
                {currentLanguage === 'ar' ? 'حرج' : 'Critical'}
              </button>
              <button
                onClick={() => setAlertFilter('warning')}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  alertFilter === 'warning' ?'bg-warning text-white' :'text-text-secondary hover:text-text-primary'
                }`}
              >
                {currentLanguage === 'ar' ? 'تحذير' : 'Warning'}
              </button>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-error-50 border border-error-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-error">
                  {currentLanguage === 'ar' ? 'تنبيهات حرجة' : 'Critical Alerts'}
                </p>
                <p className="text-2xl font-bold text-error">
                  {lowStockItems.filter(item => item.severity === 'critical').length}
                </p>
              </div>
              <Icon name="AlertTriangle" size={32} className="text-error opacity-60" />
            </div>
          </div>
          
          <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-warning">
                  {currentLanguage === 'ar' ? 'تحذيرات' : 'Warnings'}
                </p>
                <p className="text-2xl font-bold text-warning">
                  {lowStockItems.filter(item => item.severity === 'warning').length}
                </p>
              </div>
              <Icon name="AlertCircle" size={32} className="text-warning opacity-60" />
            </div>
          </div>
          
          <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary">
                  {currentLanguage === 'ar' ? 'إجمالي التنبيهات' : 'Total Alerts'}
                </p>
                <p className="text-2xl font-bold text-primary">
                  {lowStockItems.length}
                </p>
              </div>
              <Icon name="Package" size={32} className="text-primary opacity-60" />
            </div>
          </div>
        </div>
      </div>

      {/* Alert Items */}
      <div className="divide-y divide-border">
        {filteredItems.length > 0 ? (
          filteredItems.map((item) => (
            <div key={item.id} className="p-6 hover:bg-surface-hover transition-colors">
              <div className="flex items-start justify-between space-x-4 rtl:space-x-reverse">
                <div className="flex-1 space-y-3">
                  {/* Item Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 rtl:space-x-reverse">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getSeverityColor(item.severity)}`}>
                        <Icon name={getSeverityIcon(item.severity)} size={16} />
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-text-primary">{item.name}</h4>
                        <p className="text-xs text-text-secondary">{item.id} • {item.category}</p>
                      </div>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(item.severity)}`}>
                      {getSeverityLabel(item.severity)}
                    </span>
                  </div>

                  {/* Stock Information */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-text-secondary">{currentLanguage === 'ar' ? 'المخزون الحالي' : 'Current Stock'}</p>
                      <p className="font-semibold text-text-primary">{item.currentStock}</p>
                    </div>
                    <div>
                      <p className="text-text-secondary">{currentLanguage === 'ar' ? 'الحد الأدنى' : 'Min Threshold'}</p>
                      <p className="font-semibold text-text-primary">{item.minThreshold}</p>
                    </div>
                    <div>
                      <p className="text-text-secondary">{currentLanguage === 'ar' ? 'المستودع' : 'Warehouse'}</p>
                      <p className="font-semibold text-text-primary">{item.warehouse}</p>
                    </div>
                    <div>
                      <p className="text-text-secondary">{currentLanguage === 'ar' ? 'آخر تخزين' : 'Last Restocked'}</p>
                      <p className="font-semibold text-text-primary">{formatDate(item.lastRestocked)}</p>
                    </div>
                  </div>

                  {/* Supplier Information */}
                  <div className="bg-background rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-xs text-text-secondary">{currentLanguage === 'ar' ? 'المورد' : 'Supplier'}</p>
                        <p className="text-sm font-medium text-text-primary">{item.supplier}</p>
                      </div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Button
                          variant="outline"
                          size="sm"
                          iconName="Phone"
                          onClick={() => handleContactSupplier(item.id, item.supplier)}
                        >
                          {currentLanguage === 'ar' ? 'اتصال' : 'Contact'}
                        </Button>
                        <Button
                          variant="primary"
                          size="sm"
                          iconName="ShoppingCart"
                          onClick={() => handleRestock(item.id)}
                        >
                          {currentLanguage === 'ar' ? 'إعادة تخزين' : 'Restock'}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Stock Level Progress */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs text-text-secondary">
                      <span>{currentLanguage === 'ar' ? 'مستوى المخزون' : 'Stock Level'}</span>
                      <span>{Math.round((item.currentStock / item.minThreshold) * 100)}%</span>
                    </div>
                    <div className="w-full bg-background rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          item.severity === 'critical' ? 'bg-error' : 'bg-warning'
                        }`}
                        style={{ width: `${Math.min((item.currentStock / item.minThreshold) * 100, 100)}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="p-12 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-success-100 rounded-full flex items-center justify-center">
              <Icon name="CheckCircle" size={32} className="text-success" />
            </div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              {currentLanguage === 'ar' ? 'لا توجد تنبيهات' : 'No Alerts'}
            </h3>
            <p className="text-text-secondary">
              {currentLanguage === 'ar' 
                ? `لا توجد منتجات تطابق فلتر "${alertFilter === 'all' ? 'الكل' : alertFilter === 'critical' ? 'حرج' : 'تحذير'}"حالياً.` : `No products match the"${alertFilter}" filter currently.`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default LowStockAlerts;