import React, { useState } from 'react';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const WarehouseDetailsCenter = ({ warehouse, currentLanguage }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedProducts, setSelectedProducts] = useState([]);

  if (!warehouse) {
    return (
      <div className="h-full flex items-center justify-center bg-surface">
        <div className="text-center">
          <Icon name="Building2" size={48} className="mx-auto mb-4 text-text-muted opacity-50" />
          <h3 className="text-lg font-medium text-text-primary mb-2">
            {currentLanguage === 'ar' ? 'اختر مستودع' : 'Select a Warehouse'}
          </h3>
          <p className="text-text-secondary">
            {currentLanguage === 'ar' ? 'اختر مستودعاً من القائمة لعرض التفاصيل' : 'Choose a warehouse from the list to view details'}
          </p>
        </div>
      </div>
    );
  }

  const capacityData = [
    { name: currentLanguage === 'ar' ? 'مستخدم' : 'Used', value: warehouse.capacity, color: '#3B82F6' },
    { name: currentLanguage === 'ar' ? 'متاح' : 'Available', value: 100 - warehouse.capacity, color: '#E5E7EB' }
  ];

  const movementData = [
    { name: currentLanguage === 'ar' ? 'الاثنين' : 'Mon', inbound: 45, outbound: 32 },
    { name: currentLanguage === 'ar' ? 'الثلاثاء' : 'Tue', inbound: 52, outbound: 28 },
    { name: currentLanguage === 'ar' ? 'الأربعاء' : 'Wed', inbound: 38, outbound: 41 },
    { name: currentLanguage === 'ar' ? 'الخميس' : 'Thu', inbound: 61, outbound: 35 },
    { name: currentLanguage === 'ar' ? 'الجمعة' : 'Fri', inbound: 43, outbound: 29 },
    { name: currentLanguage === 'ar' ? 'السبت' : 'Sat', inbound: 29, outbound: 18 },
    { name: currentLanguage === 'ar' ? 'الأحد' : 'Sun', inbound: 35, outbound: 22 }
  ];

  const inventoryData = [
    {
      id: 'PRD-001',
      name: currentLanguage === 'ar' ? 'جهاز كمبيوتر محمول ديل' : 'Dell Laptop Computer',
      category: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics',
      quantity: 145,
      unitPrice: 2500.00,
      totalValue: 362500.00,
      location: 'A-01-15',
      lastMovement: '2024-01-15T10:30:00',
      status: 'in-stock'
    },
    {
      id: 'PRD-002',
      name: currentLanguage === 'ar' ? 'طابعة HP ليزر' : 'HP Laser Printer',
      category: currentLanguage === 'ar' ? 'مكتبية' : 'Office Equipment',
      quantity: 23,
      unitPrice: 850.00,
      totalValue: 19550.00,
      location: 'B-03-08',
      lastMovement: '2024-01-14T14:20:00',
      status: 'low-stock'
    },
    {
      id: 'PRD-003',
      name: currentLanguage === 'ar' ? 'كرسي مكتب مريح' : 'Ergonomic Office Chair',
      category: currentLanguage === 'ar' ? 'أثاث' : 'Furniture',
      quantity: 67,
      unitPrice: 450.00,
      totalValue: 30150.00,
      location: 'C-02-12',
      lastMovement: '2024-01-15T09:15:00',
      status: 'in-stock'
    },
    {
      id: 'PRD-004',
      name: currentLanguage === 'ar' ? 'شاشة سامسونج 27 بوصة' : 'Samsung 27" Monitor',
      category: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics',
      quantity: 8,
      unitPrice: 320.00,
      totalValue: 2560.00,
      location: 'A-02-05',
      lastMovement: '2024-01-13T16:45:00',
      status: 'critical'
    },
    {
      id: 'PRD-005',
      name: currentLanguage === 'ar' ? 'مكتب خشبي تنفيذي' : 'Executive Wooden Desk',
      category: currentLanguage === 'ar' ? 'أثاث' : 'Furniture',
      quantity: 34,
      unitPrice: 1200.00,
      totalValue: 40800.00,
      location: 'C-01-20',
      lastMovement: '2024-01-15T11:00:00',
      status: 'in-stock'
    }
  ];

  const filteredInventory = inventoryData.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedInventory = [...filteredInventory].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleSelectProduct = (productId) => {
    setSelectedProducts(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProducts.length === sortedInventory.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(sortedInventory.map(item => item.id));
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'in-stock':
        return 'text-success bg-success-50';
      case 'low-stock':
        return 'text-warning bg-warning-50';
      case 'critical':
        return 'text-error bg-error-50';
      default:
        return 'text-text-muted bg-secondary-50';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'in-stock':
        return currentLanguage === 'ar' ? 'متوفر' : 'In Stock';
      case 'low-stock':
        return currentLanguage === 'ar' ? 'مخزون منخفض' : 'Low Stock';
      case 'critical':
        return currentLanguage === 'ar' ? 'حرج' : 'Critical';
      default:
        return status;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="h-full flex flex-col bg-surface">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
              <Icon 
                name={warehouse.type === 'main' ? 'Building2' : 'Warehouse'} 
                size={24} 
                color="var(--color-primary)" 
              />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-text-primary">
                {warehouse.name}
              </h1>
              <p className="text-text-secondary">
                {warehouse.location} • {warehouse.governorate}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
              warehouse.status === 'active' ? 'text-success bg-success-50' :
              warehouse.status === 'maintenance'? 'text-warning bg-warning-50' : 'text-error bg-error-50'
            }`}>
              {warehouse.status === 'active' ? (currentLanguage === 'ar' ? 'نشط' : 'Active') :
               warehouse.status === 'maintenance' ? (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance') :
               (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')}
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-surface-secondary rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">
                  {currentLanguage === 'ar' ? 'إجمالي المنتجات' : 'Total Products'}
                </p>
                <p className="text-2xl font-semibold text-text-primary">
                  {warehouse.totalProducts.toLocaleString()}
                </p>
              </div>
              <Icon name="Package" size={20} color="var(--color-primary)" />
            </div>
          </div>
          <div className="bg-surface-secondary rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">
                  {currentLanguage === 'ar' ? 'السعة المستخدمة' : 'Capacity Used'}
                </p>
                <p className="text-2xl font-semibold text-text-primary">
                  {warehouse.capacity}%
                </p>
              </div>
              <Icon name="BarChart3" size={20} color="var(--color-warning)" />
            </div>
          </div>
          <div className="bg-surface-secondary rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">
                  {currentLanguage === 'ar' ? 'القيمة الإجمالية' : 'Total Value'}
                </p>
                <p className="text-2xl font-semibold text-text-primary">
                  {formatCurrency(warehouse.totalValue)}
                </p>
              </div>
              <Icon name="DollarSign" size={20} color="var(--color-success)" />
            </div>
          </div>
          <div className="bg-surface-secondary rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">
                  {currentLanguage === 'ar' ? 'الحركات اليوم' : 'Today\'s Movements'}
                </p>
                <p className="text-2xl font-semibold text-text-primary">
                  {warehouse.todayMovements}
                </p>
              </div>
              <Icon name="TruckIcon" size={20} color="var(--color-accent)" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="p-6 border-b border-border">
        <div className="grid grid-cols-2 gap-6">
          {/* Capacity Chart */}
          <div className="bg-surface-secondary rounded-lg p-4">
            <h3 className="text-lg font-medium text-text-primary mb-4">
              {currentLanguage === 'ar' ? 'استخدام السعة' : 'Capacity Utilization'}
            </h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={capacityData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {capacityData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value}%`} />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="flex justify-center space-x-4 rtl:space-x-reverse mt-2">
              {capacityData.map((entry, index) => (
                <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-sm text-text-secondary">{entry.name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Movement Chart */}
          <div className="bg-surface-secondary rounded-lg p-4">
            <h3 className="text-lg font-medium text-text-primary mb-4">
              {currentLanguage === 'ar' ? 'حركة المنتجات الأسبوعية' : 'Weekly Product Movement'}
            </h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={movementData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar 
                    dataKey="inbound" 
                    fill="var(--color-success)" 
                    name={currentLanguage === 'ar' ? 'وارد' : 'Inbound'} 
                  />
                  <Bar 
                    dataKey="outbound" 
                    fill="var(--color-warning)" 
                    name={currentLanguage === 'ar' ? 'صادر' : 'Outbound'} 
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>

      {/* Inventory Table */}
      <div className="flex-1 flex flex-col">
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-text-primary">
              {currentLanguage === 'ar' ? 'جرد المخزون' : 'Current Inventory'}
            </h3>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="relative">
                <Icon 
                  name="Search" 
                  size={16} 
                  className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-text-muted" 
                />
                <Input
                  type="search"
                  placeholder={currentLanguage === 'ar' ? 'البحث في المخزون...' : 'Search inventory...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 rtl:pr-10 rtl:pl-3 w-64"
                />
              </div>
              {selectedProducts.length > 0 && (
                <Button variant="outline" iconName="Download">
                  {currentLanguage === 'ar' ? 'تصدير المحدد' : 'Export Selected'}
                </Button>
              )}
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-auto">
          <table className="w-full">
            <thead className="bg-surface-secondary border-b border-border sticky top-0">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedProducts.length === sortedInventory.length && sortedInventory.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-border focus:ring-primary"
                  />
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:text-text-primary"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'المنتج' : 'Product'}</span>
                    <Icon name="ArrowUpDown" size={12} />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:text-text-primary"
                  onClick={() => handleSort('category')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'الفئة' : 'Category'}</span>
                    <Icon name="ArrowUpDown" size={12} />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:text-text-primary"
                  onClick={() => handleSort('quantity')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'الكمية' : 'Quantity'}</span>
                    <Icon name="ArrowUpDown" size={12} />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:text-text-primary"
                  onClick={() => handleSort('unitPrice')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'سعر الوحدة' : 'Unit Price'}</span>
                    <Icon name="ArrowUpDown" size={12} />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:text-text-primary"
                  onClick={() => handleSort('totalValue')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'القيمة الإجمالية' : 'Total Value'}</span>
                    <Icon name="ArrowUpDown" size={12} />
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'الموقع' : 'Location'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'الحالة' : 'Status'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'آخر حركة' : 'Last Movement'}
                </th>
              </tr>
            </thead>
            <tbody className="bg-surface divide-y divide-border">
              {sortedInventory.map((item) => (
                <tr 
                  key={item.id} 
                  className="hover:bg-surface-secondary cursor-pointer transition-colors duration-150"
                  onClick={() => handleSelectProduct(item.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(item.id)}
                      onChange={() => handleSelectProduct(item.id)}
                      className="rounded border-border focus:ring-primary"
                      onClick={(e) => e.stopPropagation()}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-text-primary">{item.name}</div>
                      <div className="text-sm text-text-secondary">{item.id}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-text-primary">{item.category}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-text-primary">
                      {item.quantity.toLocaleString()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-text-primary">
                      {formatCurrency(item.unitPrice)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-text-primary">
                      {formatCurrency(item.totalValue)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-text-primary font-mono">{item.location}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(item.status)}`}>
                      {getStatusText(item.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-text-secondary">
                      {formatDate(item.lastMovement)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default WarehouseDetailsCenter;