import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const TransferFilters = ({ onFiltersChange, currentLanguage }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    governorate: '',
    productCategory: '',
    dateRange: '',
    priority: '',
    minValue: '',
    maxValue: ''
  });

  const governorates = [
    { value: 'riyadh', label: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh' },
    { value: 'eastern', label: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province' },
    { value: 'makkah', label: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah' },
    { value: 'tabuk', label: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk' },
    { value: 'asir', label: currentLanguage === 'ar' ? 'عسير' : 'Asir' }
  ];

  const productCategories = [
    { value: 'electronics', label: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics' },
    { value: 'clothing', label: currentLanguage === 'ar' ? 'ملابس' : 'Clothing' },
    { value: 'food', label: currentLanguage === 'ar' ? 'طعام' : 'Food & Beverages' },
    { value: 'automotive', label: currentLanguage === 'ar' ? 'قطع غيار' : 'Automotive' },
    { value: 'home', label: currentLanguage === 'ar' ? 'منزلية' : 'Home & Garden' }
  ];

  const priorities = [
    { value: 'urgent', label: currentLanguage === 'ar' ? 'عاجل' : 'Urgent' },
    { value: 'high', label: currentLanguage === 'ar' ? 'عالي' : 'High' },
    { value: 'normal', label: currentLanguage === 'ar' ? 'عادي' : 'Normal' },
    { value: 'low', label: currentLanguage === 'ar' ? 'منخفض' : 'Low' }
  ];

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: '',
      governorate: '',
      productCategory: '',
      dateRange: '',
      priority: '',
      minValue: '',
      maxValue: ''
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  return (
    <div className="bg-surface border-b border-border">
      <div className="p-4">
        {/* Search and Toggle */}
        <div className="flex items-center space-x-4 rtl:space-x-reverse mb-4">
          <div className="flex-1 relative">
            <Icon 
              name="Search" 
              size={16} 
              className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-text-muted" 
            />
            <Input
              type="search"
              placeholder={currentLanguage === 'ar' ? 'البحث في التحويلات...' : 'Search transfers...'}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10 rtl:pr-10 rtl:pl-3"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setIsExpanded(!isExpanded)}
            iconName={isExpanded ? "ChevronUp" : "ChevronDown"}
            iconPosition="right"
          >
            {currentLanguage === 'ar' ? 'فلاتر متقدمة' : 'Advanced Filters'}
          </Button>
        </div>

        {/* Advanced Filters */}
        {isExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-background-secondary rounded-lg">
            {/* Governorate Filter */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'المحافظة' : 'Governorate'}
              </label>
              <select
                value={filters.governorate}
                onChange={(e) => handleFilterChange('governorate', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">{currentLanguage === 'ar' ? 'جميع المحافظات' : 'All Governorates'}</option>
                {governorates.map((gov) => (
                  <option key={gov.value} value={gov.value}>{gov.label}</option>
                ))}
              </select>
            </div>

            {/* Product Category Filter */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'فئة المنتج' : 'Product Category'}
              </label>
              <select
                value={filters.productCategory}
                onChange={(e) => handleFilterChange('productCategory', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">{currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories'}</option>
                {productCategories.map((cat) => (
                  <option key={cat.value} value={cat.value}>{cat.label}</option>
                ))}
              </select>
            </div>

            {/* Priority Filter */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'الأولوية' : 'Priority'}
              </label>
              <select
                value={filters.priority}
                onChange={(e) => handleFilterChange('priority', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">{currentLanguage === 'ar' ? 'جميع الأولويات' : 'All Priorities'}</option>
                {priorities.map((priority) => (
                  <option key={priority.value} value={priority.value}>{priority.label}</option>
                ))}
              </select>
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'نطاق التاريخ' : 'Date Range'}
              </label>
              <select
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">{currentLanguage === 'ar' ? 'جميع التواريخ' : 'All Dates'}</option>
                <option value="today">{currentLanguage === 'ar' ? 'اليوم' : 'Today'}</option>
                <option value="week">{currentLanguage === 'ar' ? 'هذا الأسبوع' : 'This Week'}</option>
                <option value="month">{currentLanguage === 'ar' ? 'هذا الشهر' : 'This Month'}</option>
                <option value="quarter">{currentLanguage === 'ar' ? 'هذا الربع' : 'This Quarter'}</option>
              </select>
            </div>

            {/* Value Range */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'نطاق القيمة (ريال)' : 'Value Range (SAR)'}
              </label>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Input
                  type="number"
                  placeholder={currentLanguage === 'ar' ? 'من' : 'Min'}
                  value={filters.minValue}
                  onChange={(e) => handleFilterChange('minValue', e.target.value)}
                  className="flex-1"
                />
                <span className="text-text-muted">-</span>
                <Input
                  type="number"
                  placeholder={currentLanguage === 'ar' ? 'إلى' : 'Max'}
                  value={filters.maxValue}
                  onChange={(e) => handleFilterChange('maxValue', e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={clearFilters}
                iconName="X"
                iconPosition="left"
                className="w-full"
              >
                {currentLanguage === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TransferFilters;