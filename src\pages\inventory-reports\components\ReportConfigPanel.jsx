import React, { useState } from 'react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import WarehouseSelector from '../../../components/ui/WarehouseSelector';

const ReportConfigPanel = ({ 
  reportTypes, 
  activeReportType, 
  onReportTypeChange, 
  filters, 
  onFiltersChange, 
  currentLanguage 
}) => {
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(true);

  const dateRangeOptions = [
    { value: 'today', label: currentLanguage === 'ar' ? 'اليوم' : 'Today' },
    { value: 'yesterday', label: currentLanguage === 'ar' ? 'أمس' : 'Yesterday' },
    { value: 'last-7-days', label: currentLanguage === 'ar' ? 'آخر 7 أيام' : 'Last 7 days' },
    { value: 'last-30-days', label: currentLanguage === 'ar' ? 'آخر 30 يوماً' : 'Last 30 days' },
    { value: 'last-3-months', label: currentLanguage === 'ar' ? 'آخر 3 أشهر' : 'Last 3 months' },
    { value: 'custom', label: currentLanguage === 'ar' ? 'نطاق مخصص' : 'Custom range' }
  ];

  const categoryOptions = [
    { value: 'electronics', label: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics' },
    { value: 'clothing', label: currentLanguage === 'ar' ? 'ملابس' : 'Clothing' },
    { value: 'food-beverages', label: currentLanguage === 'ar' ? 'طعام ومشروبات' : 'Food & Beverages' },
    { value: 'automotive', label: currentLanguage === 'ar' ? 'سيارات' : 'Automotive' },
    { value: 'home-garden', label: currentLanguage === 'ar' ? 'منزلية وحديقة' : 'Home & Garden' },
    { value: 'health-beauty', label: currentLanguage === 'ar' ? 'صحة وجمال' : 'Health & Beauty' }
  ];

  const governorateOptions = [
    { value: 'riyadh', label: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh' },
    { value: 'makkah', label: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah' },
    { value: 'eastern', label: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province' },
    { value: 'asir', label: currentLanguage === 'ar' ? 'عسير' : 'Asir' },
    { value: 'jazan', label: currentLanguage === 'ar' ? 'جازان' : 'Jazan' },
    { value: 'tabuk', label: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk' },
    { value: 'hail', label: currentLanguage === 'ar' ? 'حائل' : 'Hail' },
    { value: 'northern-borders', label: currentLanguage === 'ar' ? 'الحدود الشمالية' : 'Northern Borders' }
  ];

  const inventoryStatusOptions = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع الحالات' : 'All Status' },
    { value: 'in-stock', label: currentLanguage === 'ar' ? 'متوفر' : 'In Stock' },
    { value: 'low-stock', label: currentLanguage === 'ar' ? 'مخزون منخفض' : 'Low Stock' },
    { value: 'out-of-stock', label: currentLanguage === 'ar' ? 'نفد المخزون' : 'Out of Stock' },
    { value: 'overstock', label: currentLanguage === 'ar' ? 'مخزون زائد' : 'Overstock' }
  ];

  const handleFilterChange = (filterKey, value) => {
    const newFilters = { ...filters, [filterKey]: value };
    onFiltersChange(newFilters);
  };

  const handleMultiSelectChange = (filterKey, value, checked) => {
    const currentValues = filters[filterKey] || [];
    const newValues = checked 
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value);
    
    handleFilterChange(filterKey, newValues);
  };

  const clearFilters = () => {
    onFiltersChange({
      dateRange: 'last-30-days',
      warehouses: [],
      categories: [],
      governorates: [],
      inventoryStatus: 'all'
    });
  };

  return (
    <div className="bg-surface border border-border rounded-lg shadow-elevation-1">
      {/* Report Type Selection */}
      <div className="p-6 border-b border-border">
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          {currentLanguage === 'ar' ? 'نوع التقرير' : 'Report Type'}
        </h3>
        <div className="space-y-2">
          {reportTypes?.map((type) => (
            <div key={type.id}>
              <label className="flex items-start space-x-3 rtl:space-x-reverse p-3 rounded-lg hover:bg-surface-hover cursor-pointer transition-colors">
                <input
                  type="radio"
                  name="reportType"
                  value={type.id}
                  checked={activeReportType === type.id}
                  onChange={(e) => onReportTypeChange(e.target.value)}
                  className="mt-1 w-4 h-4 text-primary border-border focus:ring-primary focus:ring-2"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-text-primary">
                    {type.name}
                  </div>
                  <div className="text-xs text-text-secondary mt-1">
                    {type.description}
                  </div>
                </div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Filters Section */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'فلاتر التقرير' : 'Report Filters'}
          </h3>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Button
              variant="ghost"
              size="sm"
              iconName="RotateCcw"
              onClick={clearFilters}
            >
              {currentLanguage === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              iconName={isFiltersExpanded ? "ChevronUp" : "ChevronDown"}
              onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
            />
          </div>
        </div>

        {isFiltersExpanded && (
          <div className="space-y-6">
            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {currentLanguage === 'ar' ? 'النطاق الزمني' : 'Date Range'}
              </label>
              <select
                value={filters?.dateRange || 'last-30-days'}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-text-primary"
              >
                {dateRangeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Custom Date Range */}
            {filters?.dateRange === 'custom' && (
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-1">
                    {currentLanguage === 'ar' ? 'من تاريخ' : 'From Date'}
                  </label>
                  <Input
                    type="date"
                    value={filters?.startDate || ''}
                    onChange={(e) => handleFilterChange('startDate', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-1">
                    {currentLanguage === 'ar' ? 'إلى تاريخ' : 'To Date'}
                  </label>
                  <Input
                    type="date"
                    value={filters?.endDate || ''}
                    onChange={(e) => handleFilterChange('endDate', e.target.value)}
                  />
                </div>
              </div>
            )}

            {/* Warehouse Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {currentLanguage === 'ar' ? 'المستودعات' : 'Warehouses'}
              </label>
              <WarehouseSelector
                value={filters?.warehouses || []}
                onChange={(warehouses) => handleFilterChange('warehouses', warehouses)}
                multiple
                placeholder={currentLanguage === 'ar' ? 'اختر المستودعات' : 'Select warehouses'}
              />
            </div>

            {/* Product Categories Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {currentLanguage === 'ar' ? 'فئات المنتجات' : 'Product Categories'}
              </label>
              <div className="space-y-2 max-h-40 overflow-y-auto border border-border rounded-lg p-2">
                {categoryOptions.map((category) => (
                  <label key={category.value} className="flex items-center space-x-2 rtl:space-x-reverse p-1 hover:bg-surface-hover rounded cursor-pointer">
                    <input
                      type="checkbox"
                      value={category.value}
                      checked={filters?.categories?.includes(category.value) || false}
                      onChange={(e) => handleMultiSelectChange('categories', category.value, e.target.checked)}
                      className="w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2"
                    />
                    <span className="text-sm text-text-primary">{category.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Governorate Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {currentLanguage === 'ar' ? 'المحافظات' : 'Governorates'}
              </label>
              <div className="space-y-2 max-h-40 overflow-y-auto border border-border rounded-lg p-2">
                {governorateOptions.map((governorate) => (
                  <label key={governorate.value} className="flex items-center space-x-2 rtl:space-x-reverse p-1 hover:bg-surface-hover rounded cursor-pointer">
                    <input
                      type="checkbox"
                      value={governorate.value}
                      checked={filters?.governorates?.includes(governorate.value) || false}
                      onChange={(e) => handleMultiSelectChange('governorates', governorate.value, e.target.checked)}
                      className="w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2"
                    />
                    <span className="text-sm text-text-primary">{governorate.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Inventory Status Filter */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {currentLanguage === 'ar' ? 'حالة المخزون' : 'Inventory Status'}
              </label>
              <select
                value={filters?.inventoryStatus || 'all'}
                onChange={(e) => handleFilterChange('inventoryStatus', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary bg-background text-text-primary"
              >
                {inventoryStatusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportConfigPanel;