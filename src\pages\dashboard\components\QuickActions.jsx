import React, { useState, useEffect } from 'react';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const QuickActions = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const quickActions = [
    {
      id: 'add-entry',
      title: currentLanguage === 'ar' ? 'إضافة دخول منتج' : 'Add Product Entry',
      description: currentLanguage === 'ar' ? 'تسجيل منتجات جديدة واردة' : 'Register new incoming products',
      icon: 'PackagePlus',
      color: 'success',
      action: () => console.log('Add product entry')
    },
    {
      id: 'initiate-transfer',
      title: currentLanguage === 'ar' ? 'بدء تحويل' : 'Initiate Transfer',
      description: currentLanguage === 'ar' ? 'تحويل منتجات بين المستودعات' : 'Transfer products between warehouses',
      icon: 'ArrowRightLeft',
      color: 'primary',
      action: () => console.log('Initiate transfer')
    },
    {
      id: 'generate-report',
      title: currentLanguage === 'ar' ? 'إنشاء تقرير' : 'Generate Report',
      description: currentLanguage === 'ar' ? 'إنشاء تقارير مخصصة' : 'Create custom reports',
      icon: 'FileText',
      color: 'secondary',
      action: () => console.log('Generate report')
    },
    {
      id: 'quick-inventory',
      title: currentLanguage === 'ar' ? 'جرد سريع' : 'Quick Inventory',
      description: currentLanguage === 'ar' ? 'إجراء جرد سريع للمخزون' : 'Perform quick stock count',
      icon: 'ClipboardList',
      color: 'warning',
      action: () => console.log('Quick inventory')
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      success: 'bg-success-50 hover:bg-success-100 text-success border-success-200',
      primary: 'bg-primary-50 hover:bg-primary-100 text-primary border-primary-200',
      secondary: 'bg-secondary-50 hover:bg-secondary-100 text-secondary border-secondary-200',
      warning: 'bg-warning-50 hover:bg-warning-100 text-warning border-warning-200'
    };
    return colors[color] || colors.primary;
  };

  return (
    <div className="bg-surface border border-border rounded-lg shadow-elevation-1">
      <div className="p-6 border-b border-border">
        <h3 className="text-lg font-semibold text-text-primary">
          {currentLanguage === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
        </h3>
        <p className="text-sm text-text-secondary mt-1">
          {currentLanguage === 'ar' ? 'الوصول السريع للمهام الشائعة' : 'Quick access to common tasks'}
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {quickActions.map((action) => (
            <button
              key={action.id}
              onClick={action.action}
              className={`p-4 rounded-lg border-2 border-dashed transition-all duration-200 text-left hover:border-solid ${getColorClasses(action.color)}`}
            >
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <div className="flex-shrink-0">
                  <Icon name={action.icon} size={24} />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium mb-1">
                    {action.title}
                  </h4>
                  <p className="text-xs opacity-80 leading-relaxed">
                    {action.description}
                  </p>
                </div>
                <Icon name="ArrowRight" size={16} className="opacity-60 rtl:rotate-180" />
              </div>
            </button>
          ))}
        </div>
      </div>

      <div className="p-6 border-t border-border bg-secondary-50">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-text-primary">
              {currentLanguage === 'ar' ? 'هل تحتاج مساعدة؟' : 'Need Help?'}
            </h4>
            <p className="text-xs text-text-secondary">
              {currentLanguage === 'ar' ? 'تحقق من دليل المستخدم' : 'Check out our user guide'}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            iconName="HelpCircle"
            iconPosition="left"
            onClick={() => console.log('Open help')}
          >
            {currentLanguage === 'ar' ? 'مساعدة' : 'Help'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;