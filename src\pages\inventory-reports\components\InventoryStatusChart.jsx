import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';

const InventoryStatusChart = ({ data, currentLanguage }) => {
  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-surface border border-border rounded-lg p-3 shadow-lg">
          <p className="text-text-primary font-medium">{`${label}`}</p>
          <p className="text-primary">
            {`${currentLanguage === 'ar' ? 'القيمة:' : 'Value:'} SAR ${payload[0]?.value?.toLocaleString()}`}
          </p>
          <p className="text-text-secondary">
            {`${currentLanguage === 'ar' ? 'العناصر:' : 'Items:'} ${payload[0]?.payload?.items?.toLocaleString()}`}
          </p>
          <p className="text-text-secondary">
            {`${currentLanguage === 'ar' ? 'المستودعات:' : 'Warehouses:'} ${payload[0]?.payload?.warehouses}`}
          </p>
        </div>
      );
    }
    return null;
  };

  const PieTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-surface border border-border rounded-lg p-3 shadow-lg">
          <p className="text-text-primary font-medium">{data.payload.category}</p>
          <p className="text-primary">
            {`${currentLanguage === 'ar' ? 'القيمة:' : 'Value:'} SAR ${data.value?.toLocaleString()}`}
          </p>
          <p className="text-text-secondary">
            {`${currentLanguage === 'ar' ? 'النسبة:' : 'Percentage:'} ${((data.value / data.payload.total) * 100).toFixed(1)}%`}
          </p>
        </div>
      );
    }
    return null;
  };

  // Calculate total for pie chart
  const totalValue = data?.reduce((sum, item) => sum + item.value, 0) || 0;
  const pieData = data?.map(item => ({
    ...item,
    total: totalValue
  })) || [];

  return (
    <div className="space-y-6">
      {/* Bar Chart */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'قيمة المخزون حسب الفئة' : 'Inventory Value by Category'}
          </h3>
          <div className="text-sm text-text-secondary">
            {currentLanguage === 'ar' ? 'بالريال السعودي' : 'in SAR'}
          </div>
        </div>
        
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis 
                dataKey="category" 
                stroke="#6B7280"
                fontSize={12}
                tick={{ fill: '#6B7280' }}
              />
              <YAxis 
                stroke="#6B7280"
                fontSize={12}
                tick={{ fill: '#6B7280' }}
                tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="value" 
                fill="#3B82F6" 
                radius={[4, 4, 0, 0]}
                className="hover:opacity-80 transition-opacity"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Pie Chart */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'توزيع المخزون' : 'Inventory Distribution'}
          </h3>
          <div className="text-sm text-text-secondary">
            {currentLanguage === 'ar' ? 'حسب الفئة' : 'by Category'}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ category, value }) => `${category}: ${((value / totalValue) * 100).toFixed(1)}%`}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip content={<PieTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>

          <div className="space-y-3">
            <h4 className="text-sm font-medium text-text-primary mb-3">
              {currentLanguage === 'ar' ? 'تفاصيل الفئات' : 'Category Details'}
            </h4>
            {data?.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-background rounded-lg">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div 
                    className="w-4 h-4 rounded-full" 
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <div>
                    <div className="text-sm font-medium text-text-primary">{item.category}</div>
                    <div className="text-xs text-text-secondary">
                      {item.items?.toLocaleString()} {currentLanguage === 'ar' ? 'عنصر' : 'items'}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-text-primary">
                    SAR {item.value?.toLocaleString()}
                  </div>
                  <div className="text-xs text-text-secondary">
                    {((item.value / totalValue) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventoryStatusChart;