import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import HeaderNavigation from '../../components/ui/HeaderNavigation';
import ContextualSidebar from '../../components/ui/ContextualSidebar';
import BreadcrumbNavigation from '../../components/ui/BreadcrumbNavigation';
import Button from '../../components/ui/Button';
import ReportConfigPanel from './components/ReportConfigPanel';
import ReportSummaryCards from './components/ReportSummaryCards';
import InventoryStatusChart from './components/InventoryStatusChart';
import StockMovementTable from './components/StockMovementTable';
import WarehouseCapacityChart from './components/WarehouseCapacityChart';
import LowStockAlerts from './components/LowStockAlerts';
import ExportPanel from './components/ExportPanel';

const InventoryReports = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [activeReportType, setActiveReportType] = useState('current-inventory');
  const [reportFilters, setReportFilters] = useState({
    dateRange: 'last-30-days',
    warehouses: [],
    categories: [],
    governorates: [],
    inventoryStatus: 'all'
  });
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [reportData, setReportData] = useState(null);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  // Mock data for summary cards
  const summaryData = [
    {
      title: currentLanguage === 'ar' ? 'إجمالي قيمة المخزون' : 'Total Inventory Value',
      value: 'SAR 2.4M',
      trend: '+12.5%',
      trendDirection: 'up',
      icon: 'DollarSign',
      color: 'primary'
    },
    {
      title: currentLanguage === 'ar' ? 'إجمالي المنتجات' : 'Total Products',
      value: '12,847',
      trend: '+3.2%',
      trendDirection: 'up',
      icon: 'Package',
      color: 'success'
    },
    {
      title: currentLanguage === 'ar' ? 'مخزون منخفض' : 'Low Stock Items',
      value: '47',
      trend: '-8.1%',
      trendDirection: 'down',
      icon: 'AlertTriangle',
      color: 'warning'
    },
    {
      title: currentLanguage === 'ar' ? 'مخزون زائد' : 'Overstock Items',
      value: '23',
      trend: '+5.3%',
      trendDirection: 'up',
      icon: 'TrendingUp',
      color: 'error'
    }
  ];

  const reportTypes = [
    {
      id: 'current-inventory',
      name: currentLanguage === 'ar' ? 'حالة المخزون الحالية' : 'Current Inventory Status',
      description: currentLanguage === 'ar' ? 'نظرة عامة على مستويات المخزون الحالية' : 'Overview of current stock levels'
    },
    {
      id: 'stock-movement',
      name: currentLanguage === 'ar' ? 'ملخص حركة المخزون' : 'Stock Movement Summary',
      description: currentLanguage === 'ar' ? 'تتبع حركة المنتجات والتحويلات' : 'Track product movements and transfers'
    },
    {
      id: 'warehouse-capacity',
      name: currentLanguage === 'ar' ? 'تحليل سعة المستودعات' : 'Warehouse Capacity Analysis',
      description: currentLanguage === 'ar' ? 'تحليل استخدام سعة المستودعات' : 'Analysis of warehouse capacity utilization'
    },
    {
      id: 'product-performance',
      name: currentLanguage === 'ar' ? 'مقاييس أداء المنتجات' : 'Product Performance Metrics',
      description: currentLanguage === 'ar' ? 'تحليل أداء المنتجات ومعدل الدوران' : 'Analysis of product performance and turnover rates'
    },
    {
      id: 'comparative-inventory',
      name: currentLanguage === 'ar' ? 'مستويات المخزون المقارنة' : 'Comparative Inventory Levels',
      description: currentLanguage === 'ar' ? 'مقارنة مستويات المخزون عبر الفترات' : 'Compare inventory levels across time periods'
    }
  ];

  const handleFiltersChange = (newFilters) => {
    setReportFilters(newFilters);
  };

  const handleGenerateReport = async () => {
    setIsGeneratingReport(true);
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock report data generation based on filters and report type
      const mockData = generateMockReportData(activeReportType, reportFilters);
      setReportData(mockData);
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const generateMockReportData = (reportType, filters) => {
    // Mock data generation logic based on report type
    const baseData = {
      generated: new Date(),
      reportType,
      filters,
      summary: summaryData
    };

    switch (reportType) {
      case 'current-inventory':
        return {
          ...baseData,
          inventoryData: [
            { category: 'Electronics', value: 450000, items: 2847, warehouses: 5 },
            { category: 'Clothing', value: 320000, items: 1923, warehouses: 4 },
            { category: 'Food & Beverages', value: 280000, items: 1756, warehouses: 3 },
            { category: 'Automotive', value: 180000, items: 892, warehouses: 2 },
            { category: 'Home & Garden', value: 150000, items: 1234, warehouses: 4 }
          ]
        };
      
      case 'stock-movement':
        return {
          ...baseData,
          movementData: [
            { date: '2024-01-01', inbound: 245, outbound: 189, transfers: 34 },
            { date: '2024-01-02', inbound: 267, outbound: 203, transfers: 28 },
            { date: '2024-01-03', inbound: 198, outbound: 234, transfers: 41 }
          ]
        };
      
      default:
        return baseData;
    }
  };

  const handleExportReport = (format) => {
    console.log(`Exporting report in ${format} format`);
    // In a real application, this would trigger the actual export functionality
  };

  const handleScheduleReport = (schedule) => {
    console.log('Scheduling report:', schedule);
    // In a real application, this would set up automated report generation
  };

  return (
    <div className="min-h-screen bg-background">
      <Helmet>
        <title>
          {currentLanguage === 'ar' ? 'تقارير المخزون - مدير المستودعات' : 'Inventory Reports - Warehouse Manager'}
        </title>
        <meta 
          name="description" 
          content={currentLanguage === 'ar' ? 'تقارير شاملة لتحليل بيانات مخزون المنتجات عبر جميع مواقع المستودعات' : 'Comprehensive reports for analyzing product inventory data across all warehouse locations'} 
        />
      </Helmet>

      <HeaderNavigation />
      
      <div className="flex pt-16">
        <ContextualSidebar 
          isOpen={isSidebarOpen} 
          onToggle={() => setIsSidebarOpen(!isSidebarOpen)} 
        />
        
        <main className="flex-1 lg:ml-80 transition-all duration-300">
          <div className="p-6 max-w-full">
            {/* Header Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <Button
                    variant="ghost"
                    onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                    className="lg:hidden"
                    iconName="Menu"
                  />
                  <div>
                    <h1 className="text-3xl font-bold text-text-primary">
                      {currentLanguage === 'ar' ? 'تقارير المخزون' : 'Inventory Reports'}
                    </h1>
                    <p className="text-text-secondary mt-1">
                      {currentLanguage === 'ar' ? 'تقارير شاملة لتحليل بيانات المخزون عبر جميع المستودعات' : 'Comprehensive reports for analyzing inventory data across all warehouses'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Button 
                    variant="outline" 
                    iconName="RefreshCw" 
                    iconPosition="left"
                    onClick={() => window.location.reload()}
                  >
                    {currentLanguage === 'ar' ? 'تحديث' : 'Refresh'}
                  </Button>
                  <Button 
                    variant="primary" 
                    iconName="FileText" 
                    iconPosition="left"
                    onClick={handleGenerateReport}
                    disabled={isGeneratingReport}
                  >
                    {isGeneratingReport 
                      ? (currentLanguage === 'ar' ? 'جاري إنشاء التقرير...' : 'Generating Report...') 
                      : (currentLanguage === 'ar' ? 'إنشاء تقرير' : 'Generate Report')
                    }
                  </Button>
                </div>
              </div>
              
              <BreadcrumbNavigation 
                warehouseContext={selectedWarehouse}
                customBreadcrumbs={[
                  { name: currentLanguage === 'ar' ? 'لوحة التحكم' : 'Dashboard', path: '/dashboard' },
                  { name: currentLanguage === 'ar' ? 'التقارير' : 'Reports', path: '/reports' },
                  { name: currentLanguage === 'ar' ? 'تقارير المخزون' : 'Inventory Reports', path: '/inventory-reports' }
                ]}
              />
            </div>

            {/* Summary Cards */}
            <div className="mb-8">
              <ReportSummaryCards 
                data={summaryData}
                currentLanguage={currentLanguage}
              />
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 xl:grid-cols-12 gap-6">
              {/* Report Configuration Panel - Left Sidebar */}
              <div className="xl:col-span-4">
                <ReportConfigPanel
                  reportTypes={reportTypes}
                  activeReportType={activeReportType}
                  onReportTypeChange={setActiveReportType}
                  filters={reportFilters}
                  onFiltersChange={handleFiltersChange}
                  currentLanguage={currentLanguage}
                />
              </div>

              {/* Main Report Display Area */}
              <div className="xl:col-span-8 space-y-6">
                {/* Report Type Tabs - Mobile/Tablet View */}
                <div className="xl:hidden">
                  <div className="flex overflow-x-auto space-x-2 pb-2">
                    {reportTypes.map((type) => (
                      <button
                        key={type.id}
                        onClick={() => setActiveReportType(type.id)}
                        className={`flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                          activeReportType === type.id
                            ? 'bg-primary text-white' :'bg-surface text-text-secondary hover:bg-surface-hover'
                        }`}
                      >
                        {type.name}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Report Content */}
                {reportData && (
                  <div className="space-y-6">
                    {/* Current Inventory Status Report */}
                    {activeReportType === 'current-inventory' && (
                      <>
                        <InventoryStatusChart
                          data={reportData.inventoryData}
                          currentLanguage={currentLanguage}
                        />
                        <LowStockAlerts
                          currentLanguage={currentLanguage}
                        />
                      </>
                    )}

                    {/* Stock Movement Report */}
                    {activeReportType === 'stock-movement' && (
                      <StockMovementTable
                        data={reportData.movementData}
                        currentLanguage={currentLanguage}
                      />
                    )}

                    {/* Warehouse Capacity Report */}
                    {activeReportType === 'warehouse-capacity' && (
                      <WarehouseCapacityChart
                        currentLanguage={currentLanguage}
                      />
                    )}

                    {/* Product Performance Report */}
                    {activeReportType === 'product-performance' && (
                      <div className="bg-surface border border-border rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">
                          {currentLanguage === 'ar' ? 'مقاييس أداء المنتجات' : 'Product Performance Metrics'}
                        </h3>
                        <p className="text-text-secondary">
                          {currentLanguage === 'ar' ? 'سيتم عرض تحليل مفصل لأداء المنتجات ومعدلات الدوران.' : 'Detailed analysis of product performance and turnover rates will be displayed here.'}
                        </p>
                      </div>
                    )}

                    {/* Comparative Inventory Report */}
                    {activeReportType === 'comparative-inventory' && (
                      <div className="bg-surface border border-border rounded-lg p-6">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">
                          {currentLanguage === 'ar' ? 'مستويات المخزون المقارنة' : 'Comparative Inventory Levels'}
                        </h3>
                        <p className="text-text-secondary">
                          {currentLanguage === 'ar' ? 'سيتم عرض مقارنة مستويات المخزون عبر فترات زمنية مختلفة.' : 'Comparison of inventory levels across different time periods will be displayed here.'}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* No Report Generated State */}
                {!reportData && (
                  <div className="bg-surface border border-border rounded-lg p-12 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-primary-100 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-text-primary mb-2">
                      {currentLanguage === 'ar' ? 'لم يتم إنشاء تقرير بعد' : 'No Report Generated Yet'}
                    </h3>
                    <p className="text-text-secondary mb-4">
                      {currentLanguage === 'ar' ? 'اختر نوع التقرير وقم بتكوين الفلاتر، ثم انقر على "إنشاء تقرير" لبدء التحليل.' : 'Select a report type and configure your filters, then click "Generate Report" to start your analysis.'}
                    </p>
                    <Button 
                      variant="primary" 
                      iconName="FileText"
                      onClick={handleGenerateReport}
                      disabled={isGeneratingReport}
                    >
                      {currentLanguage === 'ar' ? 'إنشاء التقرير الأول' : 'Generate Your First Report'}
                    </Button>
                  </div>
                )}

                {/* Export Panel */}
                {reportData && (
                  <ExportPanel
                    onExport={handleExportReport}
                    onSchedule={handleScheduleReport}
                    currentLanguage={currentLanguage}
                  />
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default InventoryReports;