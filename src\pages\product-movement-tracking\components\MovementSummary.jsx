import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const MovementSummary = ({ movements, selectedTab, className = "" }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const calculateSummary = () => {
    const filteredMovements = selectedTab === 'all' 
      ? movements 
      : movements.filter(movement => movement.movementType === selectedTab);

    const totalMovements = filteredMovements.length;
    const totalValue = filteredMovements.reduce((sum, movement) => sum + movement.totalValue, 0);
    const totalQuantity = filteredMovements.reduce((sum, movement) => sum + movement.quantity, 0);

    // Today's movements
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayMovements = filteredMovements.filter(movement => {
      const movementDate = new Date(movement.timestamp);
      movementDate.setHours(0, 0, 0, 0);
      return movementDate.getTime() === today.getTime();
    });

    const todayCount = todayMovements.length;
    const todayValue = todayMovements.reduce((sum, movement) => sum + movement.totalValue, 0);

    // Movement type breakdown
    const entryCount = filteredMovements.filter(m => m.movementType === 'entry').length;
    const exitCount = filteredMovements.filter(m => m.movementType === 'exit').length;
    const transferCount = filteredMovements.filter(m => m.movementType === 'transfer').length;

    const entryValue = filteredMovements
      .filter(m => m.movementType === 'entry')
      .reduce((sum, movement) => sum + movement.totalValue, 0);
    
    const exitValue = filteredMovements
      .filter(m => m.movementType === 'exit')
      .reduce((sum, movement) => sum + movement.totalValue, 0);

    return {
      totalMovements,
      totalValue,
      totalQuantity,
      todayCount,
      todayValue,
      entryCount,
      exitCount,
      transferCount,
      entryValue,
      exitValue
    };
  };

  const summary = calculateSummary();

  const summaryCards = [
    {
      title: currentLanguage === 'ar' ? 'إجمالي الحركات' : 'Total Movements',
      value: summary.totalMovements.toLocaleString(),
      icon: 'Activity',
      color: 'primary',
      subtitle: currentLanguage === 'ar' ? 'جميع الحركات' : 'All movements'
    },
    {
      title: currentLanguage === 'ar' ? 'القيمة الإجمالية' : 'Total Value',
      value: formatCurrency(summary.totalValue),
      icon: 'DollarSign',
      color: 'success',
      subtitle: currentLanguage === 'ar' ? 'قيمة جميع الحركات' : 'Value of all movements'
    },
    {
      title: currentLanguage === 'ar' ? 'إجمالي الكمية' : 'Total Quantity',
      value: summary.totalQuantity.toLocaleString(),
      icon: 'Package',
      color: 'accent',
      subtitle: currentLanguage === 'ar' ? 'وحدات منقولة' : 'Units moved'
    },
    {
      title: currentLanguage === 'ar' ? 'حركات اليوم' : 'Today\'s Movements',
      value: summary.todayCount.toLocaleString(),
      icon: 'Calendar',
      color: 'secondary',
      subtitle: formatCurrency(summary.todayValue)
    }
  ];

  const getColorClasses = (color) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-50',
          icon: 'var(--color-primary)',
          text: 'text-primary'
        };
      case 'success':
        return {
          bg: 'bg-success-50',
          icon: 'var(--color-success)',
          text: 'text-success'
        };
      case 'accent':
        return {
          bg: 'bg-accent-50',
          icon: 'var(--color-accent)',
          text: 'text-accent'
        };
      case 'secondary':
        return {
          bg: 'bg-secondary-100',
          icon: 'var(--color-secondary)',
          text: 'text-secondary'
        };
      default:
        return {
          bg: 'bg-primary-50',
          icon: 'var(--color-primary)',
          text: 'text-primary'
        };
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {summaryCards.map((card, index) => {
          const colors = getColorClasses(card.color);
          return (
            <div key={index} className="bg-surface border border-border rounded-lg p-6 shadow-elevation-1">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-text-secondary mb-1">
                    {card.title}
                  </p>
                  <p className="text-2xl font-bold text-text-primary mb-1">
                    {card.value}
                  </p>
                  <p className="text-xs text-text-muted">
                    {card.subtitle}
                  </p>
                </div>
                <div className={`w-12 h-12 ${colors.bg} rounded-lg flex items-center justify-center`}>
                  <Icon name={card.icon} size={24} color={colors.icon} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Movement Type Breakdown */}
      {selectedTab === 'all' && (
        <div className="bg-surface border border-border rounded-lg p-6 shadow-elevation-1">
          <h3 className="text-lg font-semibold text-text-primary mb-4">
            {currentLanguage === 'ar' ? 'تفصيل أنواع الحركات' : 'Movement Type Breakdown'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Entries */}
            <div className="text-center">
              <div className="w-16 h-16 bg-success-50 rounded-full flex items-center justify-center mx-auto mb-3">
                <Icon name="ArrowDown" size={24} color="var(--color-success)" />
              </div>
              <h4 className="text-lg font-semibold text-text-primary">
                {summary.entryCount.toLocaleString()}
              </h4>
              <p className="text-sm text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'دخول' : 'Entries'}
              </p>
              <p className="text-xs font-medium text-success">
                {formatCurrency(summary.entryValue)}
              </p>
            </div>

            {/* Exits */}
            <div className="text-center">
              <div className="w-16 h-16 bg-error-50 rounded-full flex items-center justify-center mx-auto mb-3">
                <Icon name="ArrowUp" size={24} color="var(--color-error)" />
              </div>
              <h4 className="text-lg font-semibold text-text-primary">
                {summary.exitCount.toLocaleString()}
              </h4>
              <p className="text-sm text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'خروج' : 'Exits'}
              </p>
              <p className="text-xs font-medium text-error">
                {formatCurrency(summary.exitValue)}
              </p>
            </div>

            {/* Transfers */}
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mx-auto mb-3">
                <Icon name="ArrowRightLeft" size={24} color="var(--color-primary)" />
              </div>
              <h4 className="text-lg font-semibold text-text-primary">
                {summary.transferCount.toLocaleString()}
              </h4>
              <p className="text-sm text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'تحويلات' : 'Transfers'}
              </p>
              <p className="text-xs font-medium text-primary">
                {currentLanguage === 'ar' ? 'بين المستودعات' : 'Inter-warehouse'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity Indicator */}
      <div className="bg-surface border border-border rounded-lg p-4 shadow-elevation-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <Icon name="Clock" size={16} color="var(--color-primary)" />
            </div>
            <div>
              <p className="text-sm font-medium text-text-primary">
                {currentLanguage === 'ar' ? 'آخر تحديث' : 'Last Updated'}
              </p>
              <p className="text-xs text-text-secondary">
                {new Date().toLocaleString(currentLanguage === 'ar' ? 'ar-SA' : 'en-SA')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span className="text-xs text-success font-medium">
              {currentLanguage === 'ar' ? 'مباشر' : 'Live'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MovementSummary;