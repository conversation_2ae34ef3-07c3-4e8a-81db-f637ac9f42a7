import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Icon from '../../../components/AppIcon';

const LoginForm = () => {
  const navigate = useNavigate();
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  // Mock credentials for different user roles
  const mockCredentials = [
    {
      email: '<EMAIL>',
      password: 'manager123',
      role: 'warehouse_manager',
      name: currentLanguage === 'ar' ? 'أحمد محمد' : 'Ahmed Mohammed'
    },
    {
      email: '<EMAIL>',
      password: 'supervisor123',
      role: 'regional_supervisor',
      name: currentLanguage === 'ar' ? 'فاطمة علي' : 'Fatima Ali'
    },
    {
      email: '<EMAIL>',
      password: 'analyst123',
      role: 'inventory_analyst',
      name: currentLanguage === 'ar' ? 'محمد حسن' : 'Mohammed Hassan'
    },
    {
      email: '<EMAIL>',
      password: 'director123',
      role: 'operations_director',
      name: currentLanguage === 'ar' ? 'سارة أحمد' : 'Sara Ahmed'
    },
    {
      email: '<EMAIL>',
      password: 'coordinator123',
      role: 'logistics_coordinator',
      name: currentLanguage === 'ar' ? 'عبدالله خالد' : 'Abdullah Khalid'
    },
    {
      email: '<EMAIL>',
      password: 'finance123',
      role: 'finance_personnel',
      name: currentLanguage === 'ar' ? 'نورا سالم' : 'Nora Salem'
    }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = currentLanguage === 'ar' ? 'البريد الإلكتروني مطلوب' : 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = currentLanguage === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Email is invalid';
    }

    if (!formData.password.trim()) {
      newErrors.password = currentLanguage === 'ar' ? 'كلمة المرور مطلوبة' : 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = currentLanguage === 'ar' ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' : 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Check credentials
      const user = mockCredentials.find(
        cred => cred.email === formData.email && cred.password === formData.password
      );

      if (user) {
        // Store user data
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('isAuthenticated', 'true');
        
        if (formData.rememberMe) {
          localStorage.setItem('rememberMe', 'true');
        }

        // Navigate to dashboard
        navigate('/dashboard');
      } else {
        setErrors({
          general: currentLanguage === 'ar' ? 'البريد الإلكتروني أو كلمة المرور غير صحيحة' : 'Invalid email or password'
        });
      }
    } catch (error) {
      setErrors({
        general: currentLanguage === 'ar' ? 'حدث خطأ أثناء تسجيل الدخول' : 'An error occurred during login'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    alert(currentLanguage === 'ar' ? 'ميزة استعادة كلمة المرور قيد التطوير' : 'Password recovery feature is under development');
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* General Error */}
        {errors.general && (
          <div className="p-4 bg-error-50 border border-error-200 rounded-lg flex items-center space-x-3 rtl:space-x-reverse">
            <Icon name="AlertCircle" size={20} color="var(--color-error)" />
            <p className="text-sm text-error font-medium">{errors.general}</p>
          </div>
        )}

        {/* Email Field */}
        <div className="space-y-2">
          <label htmlFor="email" className="block text-sm font-medium text-text-primary">
            {currentLanguage === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}
          </label>
          <div className="relative">
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder={currentLanguage === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email'}
              className={`w-full pl-10 rtl:pr-10 rtl:pl-3 ${errors.email ? 'border-error focus:ring-error' : ''}`}
              disabled={isLoading}
            />
            <Icon 
              name="Mail" 
              size={18} 
              className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-text-muted" 
            />
          </div>
          {errors.email && (
            <p className="text-sm text-error flex items-center space-x-1 rtl:space-x-reverse">
              <Icon name="AlertCircle" size={14} />
              <span>{errors.email}</span>
            </p>
          )}
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <label htmlFor="password" className="block text-sm font-medium text-text-primary">
            {currentLanguage === 'ar' ? 'كلمة المرور' : 'Password'}
          </label>
          <div className="relative">
            <Input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange}
              placeholder={currentLanguage === 'ar' ? 'أدخل كلمة المرور' : 'Enter your password'}
              className={`w-full pl-10 pr-10 rtl:pr-10 rtl:pl-10 ${errors.password ? 'border-error focus:ring-error' : ''}`}
              disabled={isLoading}
            />
            <Icon 
              name="Lock" 
              size={18} 
              className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-text-muted" 
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 rtl:left-3 rtl:right-auto top-1/2 transform -translate-y-1/2 text-text-muted hover:text-text-primary transition-colors duration-150"
              disabled={isLoading}
            >
              <Icon name={showPassword ? 'EyeOff' : 'Eye'} size={18} />
            </button>
          </div>
          {errors.password && (
            <p className="text-sm text-error flex items-center space-x-1 rtl:space-x-reverse">
              <Icon name="AlertCircle" size={14} />
              <span>{errors.password}</span>
            </p>
          )}
        </div>

        {/* Remember Me */}
        <div className="flex items-center justify-between">
          <label className="flex items-center space-x-2 rtl:space-x-reverse">
            <Input
              type="checkbox"
              name="rememberMe"
              checked={formData.rememberMe}
              onChange={handleInputChange}
              className="w-4 h-4"
              disabled={isLoading}
            />
            <span className="text-sm text-text-secondary">
              {currentLanguage === 'ar' ? 'تذكرني' : 'Remember me'}
            </span>
          </label>
          
          <button
            type="button"
            onClick={handleForgotPassword}
            className="text-sm text-primary hover:text-primary-600 transition-colors duration-150"
            disabled={isLoading}
          >
            {currentLanguage === 'ar' ? 'نسيت كلمة المرور؟' : 'Forgot password?'}
          </button>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="primary"
          loading={isLoading}
          disabled={isLoading}
          className="w-full"
          iconName={isLoading ? undefined : "LogIn"}
          iconPosition="left"
        >
          {isLoading 
            ? (currentLanguage === 'ar' ? 'جاري تسجيل الدخول...' : 'Signing in...') 
            : (currentLanguage === 'ar' ? 'تسجيل الدخول' : 'Sign In')
          }
        </Button>
      </form>
    </div>
  );
};

export default LoginForm;