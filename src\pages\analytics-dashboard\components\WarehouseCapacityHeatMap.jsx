import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';

const WarehouseCapacityHeatMap = ({ data, currentLanguage }) => {
  const getCapacityColor = (capacity) => {
    if (capacity >= 90) return '#DC2626'; // error
    if (capacity >= 75) return '#D97706'; // warning
    if (capacity >= 50) return '#F59E0B'; // accent
    return '#059669'; // success
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-surface border border-border rounded-lg shadow-elevation-2 p-3">
          <p className="font-medium text-text-primary">{data.name}</p>
          <p className="text-sm text-text-secondary">
            {currentLanguage === 'ar' ? 'السعة:' : 'Capacity:'} {data.capacity}%
          </p>
          <p className="text-sm text-text-secondary">
            {currentLanguage === 'ar' ? 'المنتجات:' : 'Products:'} {data.products.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-text-primary">
          {currentLanguage === 'ar' ? 'خريطة سعة المستودعات' : 'Warehouse Capacity Heat Map'}
        </h3>
        <p className="text-sm text-text-secondary">
          {currentLanguage === 'ar' ? 'مستوى الإشغال عبر جميع المواقع' : 'Occupancy levels across all locations'}
        </p>
      </div>
      <div className="card-content">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="shortName" 
                tick={{ fontSize: 12, fill: 'var(--color-text-secondary)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
              />
              <YAxis 
                tick={{ fontSize: 12, fill: 'var(--color-text-secondary)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
                label={{ 
                  value: currentLanguage === 'ar' ? 'السعة (%)' : 'Capacity (%)', 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: 'var(--color-text-secondary)' }
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="capacity" radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={getCapacityColor(entry.capacity)} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center space-x-6 rtl:space-x-reverse mt-4 pt-4 border-t border-border">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-3 h-3 rounded-full bg-success"></div>
            <span className="text-xs text-text-secondary">
              {currentLanguage === 'ar' ? '< 50%' : '< 50%'}
            </span>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-3 h-3 rounded-full bg-accent"></div>
            <span className="text-xs text-text-secondary">50-74%</span>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-3 h-3 rounded-full bg-warning"></div>
            <span className="text-xs text-text-secondary">75-89%</span>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-3 h-3 rounded-full bg-error"></div>
            <span className="text-xs text-text-secondary">
              {currentLanguage === 'ar' ? '≥ 90%' : '≥ 90%'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarehouseCapacityHeatMap;