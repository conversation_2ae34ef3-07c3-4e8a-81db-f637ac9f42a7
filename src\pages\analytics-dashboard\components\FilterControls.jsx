import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const FilterControls = ({ onFiltersChange, currentLanguage }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: '30d',
    governorate: 'all',
    warehouseType: 'all',
    productCategory: 'all',
    customStartDate: '',
    customEndDate: ''
  });

  const dateRangeOptions = [
    { value: '7d', label: currentLanguage === 'ar' ? '7 أيام' : '7 Days' },
    { value: '30d', label: currentLanguage === 'ar' ? '30 يوم' : '30 Days' },
    { value: '90d', label: currentLanguage === 'ar' ? '90 يوم' : '90 Days' },
    { value: '1y', label: currentLanguage === 'ar' ? 'سنة واحدة' : '1 Year' },
    { value: 'custom', label: currentLanguage === 'ar' ? 'مخصص' : 'Custom' }
  ];

  const governorateOptions = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع المحافظات' : 'All Governorates' },
    { value: 'riyadh', label: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh' },
    { value: 'makkah', label: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah' },
    { value: 'eastern', label: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province' },
    { value: 'asir', label: currentLanguage === 'ar' ? 'عسير' : 'Asir' },
    { value: 'tabuk', label: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk' }
  ];

  const warehouseTypeOptions = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع الأنواع' : 'All Types' },
    { value: 'main', label: currentLanguage === 'ar' ? 'رئيسي' : 'Main' },
    { value: 'regional', label: currentLanguage === 'ar' ? 'إقليمي' : 'Regional' },
    { value: 'distribution', label: currentLanguage === 'ar' ? 'توزيع' : 'Distribution' }
  ];

  const productCategoryOptions = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories' },
    { value: 'electronics', label: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics' },
    { value: 'clothing', label: currentLanguage === 'ar' ? 'ملابس' : 'Clothing' },
    { value: 'food', label: currentLanguage === 'ar' ? 'طعام' : 'Food & Beverages' },
    { value: 'automotive', label: currentLanguage === 'ar' ? 'سيارات' : 'Automotive' },
    { value: 'home', label: currentLanguage === 'ar' ? 'منزلية' : 'Home & Garden' }
  ];

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const resetFilters = () => {
    const defaultFilters = {
      dateRange: '30d',
      governorate: 'all',
      warehouseType: 'all',
      productCategory: 'all',
      customStartDate: '',
      customEndDate: ''
    };
    setFilters(defaultFilters);
    onFiltersChange?.(defaultFilters);
  };

  const exportData = () => {
    // Mock export functionality
    console.log('Exporting analytics data with filters:', filters);
  };

  return (
    <div className="card mb-6">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Icon name="Filter" size={20} color="var(--color-primary)" />
            <h3 className="text-lg font-semibold text-text-primary">
              {currentLanguage === 'ar' ? 'عوامل التصفية والتحكم' : 'Filters & Controls'}
            </h3>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Button
              variant="outline"
              onClick={exportData}
              iconName="Download"
              iconPosition="left"
            >
              {currentLanguage === 'ar' ? 'تصدير' : 'Export'}
            </Button>
            <Button
              variant="ghost"
              onClick={() => setIsExpanded(!isExpanded)}
              iconName={isExpanded ? "ChevronUp" : "ChevronDown"}
              iconPosition="right"
            >
              {isExpanded 
                ? (currentLanguage === 'ar' ? 'إخفاء' : 'Hide')
                : (currentLanguage === 'ar' ? 'إظهار المزيد' : 'Show More')
              }
            </Button>
          </div>
        </div>
      </div>
      
      <div className="card-content">
        {/* Primary Filters - Always Visible */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              {currentLanguage === 'ar' ? 'النطاق الزمني' : 'Date Range'}
            </label>
            <select
              value={filters.dateRange}
              onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
            >
              {dateRangeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Governorate */}
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              {currentLanguage === 'ar' ? 'المحافظة' : 'Governorate'}
            </label>
            <select
              value={filters.governorate}
              onChange={(e) => handleFilterChange('governorate', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
            >
              {governorateOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Warehouse Type */}
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              {currentLanguage === 'ar' ? 'نوع المستودع' : 'Warehouse Type'}
            </label>
            <select
              value={filters.warehouseType}
              onChange={(e) => handleFilterChange('warehouseType', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
            >
              {warehouseTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Product Category */}
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              {currentLanguage === 'ar' ? 'فئة المنتج' : 'Product Category'}
            </label>
            <select
              value={filters.productCategory}
              onChange={(e) => handleFilterChange('productCategory', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
            >
              {productCategoryOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Custom Date Range - Show when custom is selected */}
        {filters.dateRange === 'custom' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 p-4 bg-background-secondary rounded-lg">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'تاريخ البداية' : 'Start Date'}
              </label>
              <Input
                type="date"
                value={filters.customStartDate}
                onChange={(e) => handleFilterChange('customStartDate', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'تاريخ النهاية' : 'End Date'}
              </label>
              <Input
                type="date"
                value={filters.customEndDate}
                onChange={(e) => handleFilterChange('customEndDate', e.target.value)}
              />
            </div>
          </div>
        )}

        {/* Advanced Filters - Expandable */}
        {isExpanded && (
          <div className="border-t border-border pt-4">
            <h4 className="text-sm font-medium text-text-secondary mb-4">
              {currentLanguage === 'ar' ? 'خيارات متقدمة' : 'Advanced Options'}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'الحد الأدنى للكفاءة' : 'Min Efficiency'}
                </label>
                <Input
                  type="number"
                  placeholder="0-100"
                  min="0"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'الحد الأدنى للسعة' : 'Min Capacity'}
                </label>
                <Input
                  type="number"
                  placeholder="0-100"
                  min="0"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'حالة المستودع' : 'Warehouse Status'}
                </label>
                <select className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                  <option value="all">{currentLanguage === 'ar' ? 'جميع الحالات' : 'All Status'}</option>
                  <option value="active">{currentLanguage === 'ar' ? 'نشط' : 'Active'}</option>
                  <option value="maintenance">{currentLanguage === 'ar' ? 'صيانة' : 'Maintenance'}</option>
                  <option value="inactive">{currentLanguage === 'ar' ? 'غير نشط' : 'Inactive'}</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Filter Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-border">
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-text-secondary">
            <Icon name="Info" size={16} />
            <span>
              {currentLanguage === 'ar' ?'يتم تطبيق المرشحات تلقائياً' :'Filters are applied automatically'
              }
            </span>
          </div>
          <Button
            variant="ghost"
            onClick={resetFilters}
            iconName="RotateCcw"
            iconPosition="left"
          >
            {currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset Filters'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FilterControls;