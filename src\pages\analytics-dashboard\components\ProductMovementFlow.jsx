import React from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON>Axi<PERSON>, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';

const ProductMovementFlow = ({ data, currentLanguage }) => {
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-surface border border-border rounded-lg shadow-elevation-2 p-3">
          <p className="font-medium text-text-primary mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value.toLocaleString()}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-text-primary">
          {currentLanguage === 'ar' ? 'تدفق حركة المنتجات' : 'Product Movement Flow'}
        </h3>
        <p className="text-sm text-text-secondary">
          {currentLanguage === 'ar' ? 'الاتجاهات اليومية للدخول والخروج' : 'Daily inbound and outbound trends'}
        </p>
      </div>
      <div className="card-content">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12, fill: 'var(--color-text-secondary)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
              />
              <YAxis 
                tick={{ fontSize: 12, fill: 'var(--color-text-secondary)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
                label={{ 
                  value: currentLanguage === 'ar' ? 'عدد المنتجات' : 'Product Count', 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: 'var(--color-text-secondary)' }
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="line"
              />
              <Line 
                type="monotone" 
                dataKey="inbound" 
                stroke="var(--color-success)" 
                strokeWidth={3}
                dot={{ fill: 'var(--color-success)', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: 'var(--color-success)', strokeWidth: 2 }}
                name={currentLanguage === 'ar' ? 'الواردة' : 'Inbound'}
              />
              <Line 
                type="monotone" 
                dataKey="outbound" 
                stroke="var(--color-primary)" 
                strokeWidth={3}
                dot={{ fill: 'var(--color-primary)', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: 'var(--color-primary)', strokeWidth: 2 }}
                name={currentLanguage === 'ar' ? 'الصادرة' : 'Outbound'}
              />
              <Line 
                type="monotone" 
                dataKey="transfers" 
                stroke="var(--color-accent)" 
                strokeWidth={3}
                dot={{ fill: 'var(--color-accent)', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: 'var(--color-accent)', strokeWidth: 2 }}
                name={currentLanguage === 'ar' ? 'التحويلات' : 'Transfers'}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ProductMovementFlow;