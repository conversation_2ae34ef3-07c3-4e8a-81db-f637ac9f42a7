import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const MovementFilters = ({ onFiltersChange, isCollapsed, onToggleCollapse, className = "" }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    productCategory: '',
    warehouse: '',
    movementType: '',
    minValue: '',
    maxValue: '',
    searchTerm: ''
  });

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const productCategories = [
    { value: 'electronics', label: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics' },
    { value: 'clothing', label: currentLanguage === 'ar' ? 'ملابس' : 'Clothing' },
    { value: 'food', label: currentLanguage === 'ar' ? 'طعام' : 'Food & Beverages' },
    { value: 'automotive', label: currentLanguage === 'ar' ? 'سيارات' : 'Automotive' },
    { value: 'home', label: currentLanguage === 'ar' ? 'منزل وحديقة' : 'Home & Garden' }
  ];

  const warehouses = [
    { value: 'wh-001', label: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh' },
    { value: 'wh-002', label: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam' },
    { value: 'wh-003', label: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah' },
    { value: 'wh-004', label: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk' },
    { value: 'wh-005', label: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha' }
  ];

  const movementTypes = [
    { value: 'entry', label: currentLanguage === 'ar' ? 'دخول' : 'Entry' },
    { value: 'exit', label: currentLanguage === 'ar' ? 'خروج' : 'Exit' },
    { value: 'transfer', label: currentLanguage === 'ar' ? 'تحويل' : 'Transfer' }
  ];

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      dateFrom: '',
      dateTo: '',
      productCategory: '',
      warehouse: '',
      movementType: '',
      minValue: '',
      maxValue: '',
      searchTerm: ''
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className={`bg-surface border border-border rounded-lg shadow-elevation-1 ${className}`}>
      {/* Filter Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Icon name="Filter" size={20} color="var(--color-primary)" />
          <h3 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'تصفية الحركات' : 'Filter Movements'}
          </h3>
          {hasActiveFilters && (
            <span className="px-2 py-1 bg-primary-100 text-primary text-xs font-medium rounded-full">
              {currentLanguage === 'ar' ? 'نشط' : 'Active'}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={handleClearFilters}
              className="text-sm"
              iconName="X"
              iconPosition="left"
            >
              {currentLanguage === 'ar' ? 'مسح' : 'Clear'}
            </Button>
          )}
          <Button
            variant="ghost"
            onClick={onToggleCollapse}
            className="lg:hidden"
            iconName={isCollapsed ? "ChevronDown" : "ChevronUp"}
          >
            {isCollapsed ? (currentLanguage === 'ar' ? 'إظهار' : 'Show') : (currentLanguage === 'ar' ? 'إخفاء' : 'Hide')}
          </Button>
        </div>
      </div>

      {/* Filter Content */}
      <div className={`transition-all duration-300 ${isCollapsed ? 'hidden lg:block' : 'block'}`}>
        <div className="p-6 space-y-6">
          {/* Search Bar */}
          <div className="w-full">
            <label className="block text-sm font-medium text-text-secondary mb-2">
              {currentLanguage === 'ar' ? 'البحث' : 'Search'}
            </label>
            <div className="relative">
              <Icon 
                name="Search" 
                size={18} 
                className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-text-muted" 
              />
              <Input
                type="text"
                placeholder={currentLanguage === 'ar' ? 'البحث في المنتجات، المستخدمين، أو الملاحظات...' : 'Search products, users, or notes...'}
                value={filters.searchTerm}
                onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                className="pl-10 rtl:pr-10 rtl:pl-3"
              />
            </div>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'من تاريخ' : 'From Date'}
              </label>
              <Input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'إلى تاريخ' : 'To Date'}
              </label>
              <Input
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              />
            </div>
          </div>

          {/* Category and Warehouse */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'فئة المنتج' : 'Product Category'}
              </label>
              <select
                value={filters.productCategory}
                onChange={(e) => handleFilterChange('productCategory', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary"
              >
                <option value="">
                  {currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories'}
                </option>
                {productCategories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'المستودع' : 'Warehouse'}
              </label>
              <select
                value={filters.warehouse}
                onChange={(e) => handleFilterChange('warehouse', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary"
              >
                <option value="">
                  {currentLanguage === 'ar' ? 'جميع المستودعات' : 'All Warehouses'}
                </option>
                {warehouses.map((warehouse) => (
                  <option key={warehouse.value} value={warehouse.value}>
                    {warehouse.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Movement Type and Value Range */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'نوع الحركة' : 'Movement Type'}
              </label>
              <select
                value={filters.movementType}
                onChange={(e) => handleFilterChange('movementType', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary"
              >
                <option value="">
                  {currentLanguage === 'ar' ? 'جميع الأنواع' : 'All Types'}
                </option>
                {movementTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'أقل قيمة (ر.س)' : 'Min Value (SAR)'}
              </label>
              <Input
                type="number"
                placeholder="0"
                value={filters.minValue}
                onChange={(e) => handleFilterChange('minValue', e.target.value)}
                min="0"
                step="0.01"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                {currentLanguage === 'ar' ? 'أعلى قيمة (ر.س)' : 'Max Value (SAR)'}
              </label>
              <Input
                type="number"
                placeholder="999999"
                value={filters.maxValue}
                onChange={(e) => handleFilterChange('maxValue', e.target.value)}
                min="0"
                step="0.01"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MovementFilters;