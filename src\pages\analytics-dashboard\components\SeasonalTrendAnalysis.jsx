import React from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import Icon from '../../../components/AppIcon';


const SeasonalTrendAnalysis = ({ data, currentLanguage }) => {
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-surface border border-border rounded-lg shadow-elevation-2 p-3">
          <p className="font-medium text-text-primary mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value.toLocaleString()}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-text-primary">
          {currentLanguage === 'ar' ? 'تحليل الاتجاهات الموسمية' : 'Seasonal Trend Analysis'}
        </h3>
        <p className="text-sm text-text-secondary">
          {currentLanguage === 'ar' ? 'أنماط المخزون على مدار العام' : 'Inventory patterns throughout the year'}
        </p>
      </div>
      <div className="card-content">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <defs>
                <linearGradient id="currentYear" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="var(--color-primary)" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="var(--color-primary)" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="previousYear" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="var(--color-secondary)" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="var(--color-secondary)" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="month" 
                tick={{ fontSize: 12, fill: 'var(--color-text-secondary)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
              />
              <YAxis 
                tick={{ fontSize: 12, fill: 'var(--color-text-secondary)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
                label={{ 
                  value: currentLanguage === 'ar' ? 'قيمة المخزون' : 'Inventory Value', 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: 'var(--color-text-secondary)' }
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="rect"
              />
              <Area
                type="monotone"
                dataKey="currentYear"
                stroke="var(--color-primary)"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#currentYear)"
                name={currentLanguage === 'ar' ? 'العام الحالي' : 'Current Year'}
              />
              <Area
                type="monotone"
                dataKey="previousYear"
                stroke="var(--color-secondary)"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#previousYear)"
                name={currentLanguage === 'ar' ? 'العام السابق' : 'Previous Year'}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
        
        {/* Seasonal Insights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 pt-4 border-t border-border">
          <div className="text-center">
            <div className="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Icon name="TrendingUp" size={16} color="var(--color-success)" />
            </div>
            <p className="text-sm font-medium text-text-primary">
              {currentLanguage === 'ar' ? 'ذروة الموسم' : 'Peak Season'}
            </p>
            <p className="text-xs text-text-secondary">
              {currentLanguage === 'ar' ? 'ديسمبر - فبراير' : 'Dec - Feb'}
            </p>
          </div>
          <div className="text-center">
            <div className="w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Icon name="Minus" size={16} color="var(--color-warning)" />
            </div>
            <p className="text-sm font-medium text-text-primary">
              {currentLanguage === 'ar' ? 'الموسم المتوسط' : 'Average Season'}
            </p>
            <p className="text-xs text-text-secondary">
              {currentLanguage === 'ar' ? 'مارس - مايو' : 'Mar - May'}
            </p>
          </div>
          <div className="text-center">
            <div className="w-8 h-8 bg-error-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Icon name="TrendingDown" size={16} color="var(--color-error)" />
            </div>
            <p className="text-sm font-medium text-text-primary">
              {currentLanguage === 'ar' ? 'الموسم المنخفض' : 'Low Season'}
            </p>
            <p className="text-xs text-text-secondary">
              {currentLanguage === 'ar' ? 'يونيو - أغسطس' : 'Jun - Aug'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SeasonalTrendAnalysis;