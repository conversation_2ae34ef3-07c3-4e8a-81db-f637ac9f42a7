import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const RecentActivity = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const activities = [
    {
      id: 1,
      type: 'entry',
      title: currentLanguage === 'ar' ? 'استلام منتجات جديدة' : 'New Product Entry',
      description: currentLanguage === 'ar' ? 'تم استلام 150 وحدة من منتج الإلكترونيات في المستودع المركزي' : 'Received 150 units of Electronics Product at Central Warehouse',
      warehouse: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      timestamp: new Date(Date.now() - 300000), // 5 minutes ago
      value: 'SAR 45,000',
      icon: 'PackagePlus',
      color: 'success'
    },
    {
      id: 2,
      type: 'transfer',
      title: currentLanguage === 'ar' ? 'تحويل بين المستودعات' : 'Inter-Warehouse Transfer',
      description: currentLanguage === 'ar' ? 'تم تحويل 75 وحدة من الرياض إلى جدة' : 'Transferred 75 units from Riyadh to Jeddah',
      warehouse: currentLanguage === 'ar' ? 'الرياض → جدة' : 'Riyadh → Jeddah',
      timestamp: new Date(Date.now() - 900000), // 15 minutes ago
      value: 'SAR 22,500',
      icon: 'ArrowRightLeft',
      color: 'primary'
    },
    {
      id: 3,
      type: 'exit',
      title: currentLanguage === 'ar' ? 'خروج منتجات' : 'Product Exit',
      description: currentLanguage === 'ar' ? 'تم شحن 200 وحدة من مستودع الدمام للعميل' : 'Shipped 200 units from Dammam warehouse to customer',
      warehouse: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
      timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
      value: 'SAR 60,000',
      icon: 'PackageMinus',
      color: 'warning'
    },
    {
      id: 4,
      type: 'alert',
      title: currentLanguage === 'ar' ? 'تنبيه مخزون منخفض' : 'Low Stock Alert',
      description: currentLanguage === 'ar' ? 'مستوى المخزون أقل من الحد الأدنى لمنتج التجميل' : 'Stock level below minimum threshold for Beauty Product',
      warehouse: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
      timestamp: new Date(Date.now() - 2700000), // 45 minutes ago
      value: '25 units left',
      icon: 'AlertTriangle',
      color: 'error'
    },
    {
      id: 5,
      type: 'maintenance',
      title: currentLanguage === 'ar' ? 'صيانة مجدولة' : 'Scheduled Maintenance',
      description: currentLanguage === 'ar' ? 'بدء صيانة دورية لأنظمة التبريد' : 'Started routine maintenance for cooling systems',
      warehouse: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk',
      timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      value: 'In Progress',
      icon: 'Settings',
      color: 'secondary'
    },
    {
      id: 6,
      type: 'entry',
      title: currentLanguage === 'ar' ? 'استلام منتجات' : 'Product Received',
      description: currentLanguage === 'ar' ? 'تم استلام 300 وحدة من منتجات المنزل والحديقة' : 'Received 300 units of Home & Garden products',
      warehouse: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
      timestamp: new Date(Date.now() - 5400000), // 1.5 hours ago
      value: 'SAR 18,000',
      icon: 'PackagePlus',
      color: 'success'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      success: 'bg-success-50 text-success border-success-200',
      primary: 'bg-primary-50 text-primary border-primary-200',
      warning: 'bg-warning-50 text-warning border-warning-200',
      error: 'bg-error-50 text-error border-error-200',
      secondary: 'bg-secondary-50 text-secondary border-secondary-200'
    };
    return colors[color] || colors.primary;
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return currentLanguage === 'ar' ? 'الآن' : 'Just now';
    } else if (diffInMinutes < 60) {
      return currentLanguage === 'ar' ? `منذ ${diffInMinutes} دقيقة` : `${diffInMinutes}m ago`;
    } else {
      const diffInHours = Math.floor(diffInMinutes / 60);
      return currentLanguage === 'ar' ? `منذ ${diffInHours} ساعة` : `${diffInHours}h ago`;
    }
  };

  return (
    <div className="bg-surface border border-border rounded-lg shadow-elevation-1">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}
          </h3>
          <button className="text-sm text-primary hover:text-primary-600 font-medium transition-colors duration-150">
            {currentLanguage === 'ar' ? 'عرض الكل' : 'View All'}
          </button>
        </div>
      </div>

      <div className="max-h-96 overflow-y-auto">
        <div className="divide-y divide-border">
          {activities.map((activity) => (
            <div key={activity.id} className="p-6 hover:bg-secondary-50 transition-colors duration-150">
              <div className="flex items-start space-x-4 rtl:space-x-reverse">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${getColorClasses(activity.color)}`}>
                  <Icon name={activity.icon} size={18} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-text-primary mb-1">
                        {activity.title}
                      </h4>
                      <p className="text-sm text-text-secondary mb-2 leading-relaxed">
                        {activity.description}
                      </p>
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-xs text-text-muted">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Icon name="MapPin" size={12} />
                          <span>{activity.warehouse}</span>
                        </div>
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Icon name="Clock" size={12} />
                          <span>{formatTimeAgo(activity.timestamp)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right ml-4 rtl:mr-4 rtl:ml-0">
                      <p className="text-sm font-medium text-text-primary">
                        {activity.value}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="p-4 border-t border-border bg-secondary-50">
        <button className="w-full text-sm text-text-secondary hover:text-primary font-medium transition-colors duration-150 py-2">
          {currentLanguage === 'ar' ? 'تحميل المزيد من الأنشطة' : 'Load More Activities'}
        </button>
      </div>
    </div>
  );
};

export default RecentActivity;