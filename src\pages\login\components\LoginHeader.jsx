import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const LoginHeader = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const handleLanguageToggle = () => {
    const newLanguage = currentLanguage === 'en' ? 'ar' : 'en';
    setCurrentLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
    window.dispatchEvent(new CustomEvent('languageChange', { detail: newLanguage }));
  };

  return (
    <div className="text-center space-y-6">
      {/* Language Toggle */}
      <div className="flex justify-end">
        <Button
          variant="ghost"
          onClick={handleLanguageToggle}
          className="p-2"
          title={currentLanguage === 'ar' ? 'Switch to English' : 'التبديل إلى العربية'}
        >
          <Icon name="Languages" size={18} />
        </Button>
      </div>

      {/* Logo */}
      <div className="flex justify-center">
        <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary-600 rounded-2xl flex items-center justify-center shadow-elevation-2">
          <Icon name="Package" size={32} color="white" />
        </div>
      </div>

      {/* Title and Description */}
      <div className="space-y-2">
        <h1 className="text-2xl font-bold text-text-primary font-heading">
          {currentLanguage === 'ar' ? 'مدير المستودعات' : 'Warehouse Manager'}
        </h1>
        <p className="text-text-secondary">
          {currentLanguage === 'ar' ?'قم بتسجيل الدخول للوصول إلى نظام إدارة المستودعات' :'Sign in to access your warehouse management system'
          }
        </p>
      </div>

      {/* Demo Credentials Info */}
      <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 text-left rtl:text-right">
        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
          <Icon name="Info" size={16} color="var(--color-primary)" />
          <h3 className="text-sm font-medium text-primary">
            {currentLanguage === 'ar' ? 'بيانات تجريبية' : 'Demo Credentials'}
          </h3>
        </div>
        <div className="space-y-2 text-xs text-primary-700">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <p className="font-medium">
                {currentLanguage === 'ar' ? 'مدير المستودع:' : 'Warehouse Manager:'}
              </p>
              <p><EMAIL></p>
              <p>manager123</p>
            </div>
            <div>
              <p className="font-medium">
                {currentLanguage === 'ar' ? 'المشرف الإقليمي:' : 'Regional Supervisor:'}
              </p>
              <p><EMAIL></p>
              <p>supervisor123</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-2 pt-2">
            <div>
              <p className="font-medium">
                {currentLanguage === 'ar' ? 'محلل المخزون:' : 'Inventory Analyst:'}
              </p>
              <p><EMAIL></p>
              <p>analyst123</p>
            </div>
            <div>
              <p className="font-medium">
                {currentLanguage === 'ar' ? 'مدير العمليات:' : 'Operations Director:'}
              </p>
              <p><EMAIL></p>
              <p>director123</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginHeader;