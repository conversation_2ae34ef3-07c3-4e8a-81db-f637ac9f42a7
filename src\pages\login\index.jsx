import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import LoginBackground from './components/LoginBackground';
import LoginHeader from './components/LoginHeader';
import LoginForm from './components/LoginForm';

const Login = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is already authenticated
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    if (isAuthenticated === 'true') {
      navigate('/dashboard');
    }
  }, [navigate]);

  return (
    <LoginBackground>
      <div className="space-y-8">
        <LoginHeader />
        <LoginForm />
      </div>
    </LoginBackground>
  );
};

export default Login;