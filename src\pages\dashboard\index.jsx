import React, { useState, useEffect } from 'react';
import HeaderNavigation from '../../components/ui/HeaderNavigation';
import ContextualSidebar from '../../components/ui/ContextualSidebar';
import BreadcrumbNavigation from '../../components/ui/BreadcrumbNavigation';
import SummaryCard from './components/SummaryCard';
import WarehouseMap from './components/WarehouseMap';
import RecentActivity from './components/RecentActivity';
import QuickActions from './components/QuickActions';
import FilterControls from './components/FilterControls';
import Button from '../../components/ui/Button';
import Icon from '../../components/AppIcon';

const Dashboard = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [filters, setFilters] = useState({});
  const [lastUpdated, setLastUpdated] = useState(new Date());

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const summaryData = [
    {
      title: currentLanguage === 'ar' ? 'إجمالي المستودعات' : 'Total Warehouses',
      value: '5',
      subtitle: currentLanguage === 'ar' ? 'عبر جميع المحافظات' : 'Across all governorates',
      icon: 'Building2',
      trend: '+1',
      trendDirection: 'up',
      color: 'primary'
    },
    {
      title: currentLanguage === 'ar' ? 'التحويلات النشطة' : 'Active Transfers',
      value: '23',
      subtitle: currentLanguage === 'ar' ? 'قيد التنفيذ' : 'In progress',
      icon: 'ArrowRightLeft',
      trend: '+15%',
      trendDirection: 'up',
      color: 'success'
    },
    {
      title: currentLanguage === 'ar' ? 'حركات اليوم' : 'Today\'s Movements',
      value: '469',
      subtitle: currentLanguage === 'ar' ? 'دخول وخروج' : 'Entries & exits',
      icon: 'TruckIcon',
      trend: '+8%',
      trendDirection: 'up',
      color: 'warning'
    },
    {
      title: currentLanguage === 'ar' ? 'قيمة المخزون الإجمالية' : 'Total Inventory Value',
      value: 'SAR 2.4M',
      subtitle: currentLanguage === 'ar' ? 'جميع المستودعات' : 'All warehouses',
      icon: 'DollarSign',
      trend: '+12%',
      trendDirection: 'up',
      color: 'primary'
    }
  ];

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    // In a real app, this would trigger data refetch
    console.log('Filters changed:', newFilters);
  };

  const formatLastUpdated = () => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - lastUpdated) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return currentLanguage === 'ar' ? 'الآن' : 'Just now';
    } else if (diffInMinutes < 60) {
      return currentLanguage === 'ar' ? `منذ ${diffInMinutes} دقيقة` : `${diffInMinutes}m ago`;
    } else {
      const diffInHours = Math.floor(diffInMinutes / 60);
      return currentLanguage === 'ar' ? `منذ ${diffInHours} ساعة` : `${diffInHours}h ago`;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header Navigation */}
      <HeaderNavigation />

      {/* Main Layout */}
      <div className="flex pt-16">
        {/* Contextual Sidebar */}
        <ContextualSidebar 
          isOpen={isSidebarOpen} 
          onToggle={() => setIsSidebarOpen(!isSidebarOpen)} 
        />

        {/* Main Content */}
        <main className="flex-1 lg:ml-80 transition-all duration-300">
          <div className="p-6 max-w-full">
            {/* Page Header */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <Button
                    variant="ghost"
                    onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                    className="lg:hidden"
                    iconName="Menu"
                  />
                  <div>
                    <h1 className="text-2xl font-bold text-text-primary">
                      {currentLanguage === 'ar' ? 'لوحة التحكم' : 'Dashboard'}
                    </h1>
                    <p className="text-text-secondary">
                      {currentLanguage === 'ar' ? 'نظرة عامة على عمليات المستودعات' : 'Overview of warehouse operations'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="text-sm text-text-muted">
                    {currentLanguage === 'ar' ? 'آخر تحديث:' : 'Last updated:'} {formatLastUpdated()}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    iconName="RefreshCw"
                    onClick={() => setLastUpdated(new Date())}
                  >
                    {currentLanguage === 'ar' ? 'تحديث' : 'Refresh'}
                  </Button>
                </div>
              </div>

              {/* Breadcrumb Navigation */}
              <BreadcrumbNavigation warehouseContext={selectedWarehouse} />
            </div>

            {/* Filter Controls */}
            <div className="mb-6">
              <FilterControls onFiltersChange={handleFiltersChange} />
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {summaryData.map((card, index) => (
                <SummaryCard
                  key={index}
                  title={card.title}
                  value={card.value}
                  subtitle={card.subtitle}
                  icon={card.icon}
                  trend={card.trend}
                  trendDirection={card.trendDirection}
                  color={card.color}
                />
              ))}
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
              {/* Warehouse Map - Takes 2 columns on xl screens */}
              <div className="xl:col-span-2">
                <WarehouseMap />
              </div>

              {/* Quick Actions */}
              <div className="xl:col-span-1">
                <QuickActions />
              </div>
            </div>

            {/* Recent Activity */}
            <div className="mb-8">
              <RecentActivity />
            </div>

            {/* Additional Stats Row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-surface border border-border rounded-lg p-6 shadow-elevation-1">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-text-primary">
                    {currentLanguage === 'ar' ? 'أداء المستودعات' : 'Warehouse Performance'}
                  </h3>
                  <Icon name="TrendingUp" size={20} className="text-success" />
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'متوسط الكفاءة' : 'Average Efficiency'}
                    </span>
                    <span className="text-sm font-medium text-success">94.2%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'وقت المعالجة' : 'Processing Time'}
                    </span>
                    <span className="text-sm font-medium text-text-primary">2.3h</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'معدل الأخطاء' : 'Error Rate'}
                    </span>
                    <span className="text-sm font-medium text-error">0.8%</span>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-lg p-6 shadow-elevation-1">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-text-primary">
                    {currentLanguage === 'ar' ? 'التنبيهات النشطة' : 'Active Alerts'}
                  </h3>
                  <Icon name="AlertTriangle" size={20} className="text-warning" />
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'مخزون منخفض' : 'Low Stock'}
                    </span>
                    <span className="px-2 py-1 bg-warning-100 text-warning text-xs rounded-full">7</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'تحويلات متأخرة' : 'Delayed Transfers'}
                    </span>
                    <span className="px-2 py-1 bg-error-100 text-error text-xs rounded-full">3</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'صيانة مجدولة' : 'Scheduled Maintenance'}
                    </span>
                    <span className="px-2 py-1 bg-primary-100 text-primary text-xs rounded-full">2</span>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-lg p-6 shadow-elevation-1">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-text-primary">
                    {currentLanguage === 'ar' ? 'إحصائيات سريعة' : 'Quick Stats'}
                  </h3>
                  <Icon name="BarChart3" size={20} className="text-primary" />
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'إجمالي المنتجات' : 'Total Products'}
                    </span>
                    <span className="text-sm font-medium text-text-primary">12,847</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'المستخدمون النشطون' : 'Active Users'}
                    </span>
                    <span className="text-sm font-medium text-success">24</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-text-secondary">
                      {currentLanguage === 'ar' ? 'متوسط السعة' : 'Average Capacity'}
                    </span>
                    <span className="text-sm font-medium text-warning">65.6%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;