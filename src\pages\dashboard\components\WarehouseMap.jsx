import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';

const WarehouseMap = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const warehouses = [
    {
      id: 'wh-001',
      name: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      governorate: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh',
      capacity: 85,
      status: 'active',
      type: 'main',
      lat: 24.7136,
      lng: 46.6753,
      totalProducts: 2847,
      todayMovements: 156
    },
    {
      id: 'wh-002',
      name: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
      governorate: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province',
      capacity: 72,
      status: 'active',
      type: 'regional',
      lat: 26.4207,
      lng: 50.0888,
      totalProducts: 1923,
      todayMovements: 89
    },
    {
      id: 'wh-003',
      name: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
      governorate: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah',
      capacity: 68,
      status: 'active',
      type: 'regional',
      lat: 21.3099,
      lng: 39.8208,
      totalProducts: 2156,
      todayMovements: 134
    },
    {
      id: 'wh-004',
      name: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk',
      governorate: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk',
      capacity: 45,
      status: 'maintenance',
      type: 'regional',
      lat: 28.3998,
      lng: 36.5700,
      totalProducts: 1456,
      todayMovements: 23
    },
    {
      id: 'wh-005',
      name: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
      governorate: currentLanguage === 'ar' ? 'عسير' : 'Asir',
      capacity: 58,
      status: 'active',
      type: 'regional',
      lat: 18.2164,
      lng: 42.5053,
      totalProducts: 1789,
      todayMovements: 67
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-success border-success-300';
      case 'maintenance':
        return 'bg-warning border-warning-300';
      case 'inactive':
        return 'bg-error border-error-300';
      default:
        return 'bg-secondary-400 border-secondary-500';
    }
  };

  const getCapacityColor = (capacity) => {
    if (capacity >= 80) return 'text-error';
    if (capacity >= 60) return 'text-warning';
    return 'text-success';
  };

  return (
    <div className="bg-surface border border-border rounded-lg shadow-elevation-1 overflow-hidden">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'خريطة المستودعات' : 'Warehouse Locations'}
          </h3>
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
              <div className="w-3 h-3 bg-success rounded-full"></div>
              <span className="text-text-secondary">
                {currentLanguage === 'ar' ? 'نشط' : 'Active'}
              </span>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
              <div className="w-3 h-3 bg-warning rounded-full"></div>
              <span className="text-text-secondary">
                {currentLanguage === 'ar' ? 'صيانة' : 'Maintenance'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="relative">
        {/* Map Container */}
        <div className="h-96 bg-secondary-50 relative overflow-hidden">
          <iframe
            width="100%"
            height="100%"
            loading="lazy"
            title="Saudi Arabia Warehouses Map"
            referrerPolicy="no-referrer-when-downgrade"
            src="https://www.google.com/maps?q=24.7136,46.6753&z=6&output=embed"
            className="absolute inset-0"
          />
          
          {/* Warehouse Markers Overlay */}
          <div className="absolute inset-0 pointer-events-none">
            {warehouses.map((warehouse, index) => (
              <div
                key={warehouse.id}
                className={`absolute transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto cursor-pointer transition-all duration-200 hover:scale-110 ${
                  index === 0 ? 'top-1/2 left-1/2' :
                  index === 1 ? 'top-1/3 right-1/4' :
                  index === 2 ? 'top-2/3 left-1/4' :
                  index === 3 ? 'top-1/4 left-1/2': 'bottom-1/4 left-1/2'
                }`}
                onClick={() => setSelectedWarehouse(warehouse)}
              >
                <div className={`w-8 h-8 rounded-full border-2 ${getStatusColor(warehouse.status)} shadow-lg flex items-center justify-center`}>
                  <Icon 
                    name={warehouse.type === 'main' ? 'Building2' : 'Warehouse'} 
                    size={16} 
                    color="white" 
                  />
                </div>
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-surface border border-border rounded px-2 py-1 shadow-lg min-w-max opacity-0 hover:opacity-100 transition-opacity duration-200">
                  <p className="text-xs font-medium text-text-primary">{warehouse.name}</p>
                  <p className="text-xs text-text-secondary">{warehouse.capacity}% {currentLanguage === 'ar' ? 'ممتلئ' : 'Full'}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Warehouse Details Panel */}
        {selectedWarehouse && (
          <div className="absolute bottom-4 left-4 right-4 bg-surface border border-border rounded-lg shadow-elevation-2 p-4 z-10">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getStatusColor(selectedWarehouse.status)}`}>
                    <Icon 
                      name={selectedWarehouse.type === 'main' ? 'Building2' : 'Warehouse'} 
                      size={20} 
                      color="white" 
                    />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-text-primary">{selectedWarehouse.name}</h4>
                    <p className="text-xs text-text-secondary">{selectedWarehouse.governorate}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 mt-3">
                  <div>
                    <p className="text-xs text-text-secondary">
                      {currentLanguage === 'ar' ? 'السعة' : 'Capacity'}
                    </p>
                    <p className={`text-sm font-medium ${getCapacityColor(selectedWarehouse.capacity)}`}>
                      {selectedWarehouse.capacity}%
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-text-secondary">
                      {currentLanguage === 'ar' ? 'إجمالي المنتجات' : 'Total Products'}
                    </p>
                    <p className="text-sm font-medium text-text-primary">
                      {selectedWarehouse.totalProducts.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-text-secondary">
                      {currentLanguage === 'ar' ? 'حركات اليوم' : 'Today\'s Movements'}
                    </p>
                    <p className="text-sm font-medium text-text-primary">
                      {selectedWarehouse.todayMovements}
                    </p>
                  </div>
                </div>
              </div>
              
              <button
                onClick={() => setSelectedWarehouse(null)}
                className="p-1 hover:bg-secondary-100 rounded transition-colors duration-150"
              >
                <Icon name="X" size={16} className="text-text-muted" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WarehouseMap;