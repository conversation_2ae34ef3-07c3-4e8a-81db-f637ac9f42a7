import React from 'react';
import Icon from '../../../components/AppIcon';

const TransferStatusTabs = ({ activeTab, onTabChange, transferCounts, currentLanguage }) => {
  const tabs = [
    {
      id: 'all',
      label: currentLanguage === 'ar' ? 'جميع التحويلات' : 'All Transfers',
      icon: 'List',
      count: transferCounts.all
    },
    {
      id: 'pending',
      label: currentLanguage === 'ar' ? 'معلق' : 'Pending',
      icon: 'Clock',
      count: transferCounts.pending,
      color: 'text-warning'
    },
    {
      id: 'in-transit',
      label: currentLanguage === 'ar' ? 'في الطريق' : 'In Transit',
      icon: 'Truck',
      count: transferCounts.inTransit,
      color: 'text-primary'
    },
    {
      id: 'completed',
      label: currentLanguage === 'ar' ? 'مكتمل' : 'Completed',
      icon: 'CheckCircle',
      count: transferCounts.completed,
      color: 'text-success'
    }
  ];

  return (
    <div className="border-b border-border bg-surface">
      <div className="flex space-x-0 rtl:space-x-reverse overflow-x-auto">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              flex items-center space-x-2 rtl:space-x-reverse px-6 py-4 text-sm font-medium border-b-2 transition-all duration-200 whitespace-nowrap
              ${activeTab === tab.id
                ? 'border-primary text-primary bg-primary-50' :'border-transparent text-text-secondary hover:text-primary hover:border-primary-200'
              }
            `}
          >
            <Icon 
              name={tab.icon} 
              size={16} 
              className={tab.color || (activeTab === tab.id ? 'text-primary' : 'text-text-muted')}
            />
            <span>{tab.label}</span>
            <span className={`
              px-2 py-0.5 text-xs rounded-full font-medium
              ${activeTab === tab.id
                ? 'bg-primary text-primary-foreground'
                : 'bg-secondary-100 text-text-secondary'
              }
            `}>
              {tab.count}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default TransferStatusTabs;