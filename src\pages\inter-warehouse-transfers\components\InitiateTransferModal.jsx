import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const InitiateTransferModal = ({ isOpen, onClose, onSubmit, currentLanguage }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [transferData, setTransferData] = useState({
    originWarehouse: '',
    destinationWarehouse: '',
    products: [],
    priority: 'normal',
    notes: '',
    approvalRoute: 'standard'
  });

  const warehouses = [
    { id: 'wh-001', name: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh' },
    { id: 'wh-002', name: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam' },
    { id: 'wh-003', name: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah' },
    { id: 'wh-004', name: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk' },
    { id: 'wh-005', name: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha' }
  ];

  const availableProducts = [
    { id: 'p-001', name: currentLanguage === 'ar' ? 'لابتوب ديل XPS 13' : 'Dell XPS 13 Laptop', stock: 45, price: 3500 },
    { id: 'p-002', name: currentLanguage === 'ar' ? 'آيفون 15 برو' : 'iPhone 15 Pro', stock: 23, price: 4200 },
    { id: 'p-003', name: currentLanguage === 'ar' ? 'سامسونج جالاكسي S24' : 'Samsung Galaxy S24', stock: 67, price: 2800 },
    { id: 'p-004', name: currentLanguage === 'ar' ? 'ماك بوك برو 16 إنش' : 'MacBook Pro 16"', stock: 12, price: 8500 },
    { id: 'p-005', name: currentLanguage === 'ar' ? 'آيباد برو 12.9' : 'iPad Pro 12.9"', stock: 34, price: 3800 }
  ];

  const steps = [
    { id: 1, title: currentLanguage === 'ar' ? 'اختيار المستودعات' : 'Warehouse Selection' },
    { id: 2, title: currentLanguage === 'ar' ? 'اختيار المنتجات' : 'Product Selection' },
    { id: 3, title: currentLanguage === 'ar' ? 'تفاصيل التحويل' : 'Transfer Details' },
    { id: 4, title: currentLanguage === 'ar' ? 'المراجعة والتأكيد' : 'Review & Confirm' }
  ];

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleProductSelection = (product, quantity) => {
    const existingIndex = transferData.products.findIndex(p => p.id === product.id);
    const newProducts = [...transferData.products];
    
    if (quantity > 0) {
      if (existingIndex >= 0) {
        newProducts[existingIndex] = { ...product, quantity, totalValue: product.price * quantity };
      } else {
        newProducts.push({ ...product, quantity, totalValue: product.price * quantity });
      }
    } else {
      if (existingIndex >= 0) {
        newProducts.splice(existingIndex, 1);
      }
    }
    
    setTransferData({ ...transferData, products: newProducts });
  };

  const handleSubmit = () => {
    onSubmit(transferData);
    onClose();
    setCurrentStep(1);
    setTransferData({
      originWarehouse: '',
      destinationWarehouse: '',
      products: [],
      priority: 'normal',
      notes: '',
      approvalRoute: 'standard'
    });
  };

  const getTotalValue = () => {
    return transferData.products.reduce((sum, product) => sum + product.totalValue, 0);
  };

  const getTotalQuantity = () => {
    return transferData.products.reduce((sum, product) => sum + product.quantity, 0);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-surface rounded-lg shadow-elevation-3 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">
              {currentLanguage === 'ar' ? 'إنشاء تحويل جديد' : 'Initiate New Transfer'}
            </h2>
            <p className="text-sm text-text-muted mt-1">
              {currentLanguage === 'ar' ? `الخطوة ${currentStep} من ${steps.length}` : `Step ${currentStep} of ${steps.length}`}
            </p>
          </div>
          <Button variant="ghost" onClick={onClose} className="p-2">
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep >= step.id ? 'bg-primary text-white' : 'bg-secondary-200 text-text-muted'
                }`}>
                  {currentStep > step.id ? <Icon name="Check" size={16} /> : step.id}
                </div>
                <span className={`ml-2 rtl:mr-2 text-sm ${
                  currentStep >= step.id ? 'text-text-primary font-medium' : 'text-text-muted'
                }`}>
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-primary' : 'bg-secondary-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-96">
          {/* Step 1: Warehouse Selection */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'المستودع المصدر' : 'Origin Warehouse'}
                </label>
                <select
                  value={transferData.originWarehouse}
                  onChange={(e) => setTransferData({ ...transferData, originWarehouse: e.target.value })}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">{currentLanguage === 'ar' ? 'اختر المستودع المصدر' : 'Select Origin Warehouse'}</option>
                  {warehouses.map((warehouse) => (
                    <option key={warehouse.id} value={warehouse.id}>{warehouse.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'المستودع الوجهة' : 'Destination Warehouse'}
                </label>
                <select
                  value={transferData.destinationWarehouse}
                  onChange={(e) => setTransferData({ ...transferData, destinationWarehouse: e.target.value })}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">{currentLanguage === 'ar' ? 'اختر المستودع الوجهة' : 'Select Destination Warehouse'}</option>
                  {warehouses.filter(w => w.id !== transferData.originWarehouse).map((warehouse) => (
                    <option key={warehouse.id} value={warehouse.id}>{warehouse.name}</option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {/* Step 2: Product Selection */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-text-primary">
                {currentLanguage === 'ar' ? 'اختر المنتجات للتحويل' : 'Select Products to Transfer'}
              </h3>
              <div className="space-y-3">
                {availableProducts.map((product) => {
                  const selectedProduct = transferData.products.find(p => p.id === product.id);
                  const selectedQuantity = selectedProduct ? selectedProduct.quantity : 0;
                  
                  return (
                    <div key={product.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-text-primary">{product.name}</div>
                        <div className="text-sm text-text-muted">
                          {currentLanguage === 'ar' ? 'متوفر:' : 'Available:'} {product.stock} {currentLanguage === 'ar' ? 'وحدة' : 'units'} • {product.price.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                        </div>
                      </div>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Input
                          type="number"
                          min="0"
                          max={product.stock}
                          value={selectedQuantity}
                          onChange={(e) => handleProductSelection(product, parseInt(e.target.value) || 0)}
                          placeholder="0"
                          className="w-20 text-center"
                        />
                        <span className="text-sm text-text-muted">
                          {currentLanguage === 'ar' ? 'وحدة' : 'units'}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Step 3: Transfer Details */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'الأولوية' : 'Priority'}
                </label>
                <select
                  value={transferData.priority}
                  onChange={(e) => setTransferData({ ...transferData, priority: e.target.value })}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="low">{currentLanguage === 'ar' ? 'منخفض' : 'Low'}</option>
                  <option value="normal">{currentLanguage === 'ar' ? 'عادي' : 'Normal'}</option>
                  <option value="high">{currentLanguage === 'ar' ? 'عالي' : 'High'}</option>
                  <option value="urgent">{currentLanguage === 'ar' ? 'عاجل' : 'Urgent'}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'مسار الموافقة' : 'Approval Route'}
                </label>
                <select
                  value={transferData.approvalRoute}
                  onChange={(e) => setTransferData({ ...transferData, approvalRoute: e.target.value })}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="standard">{currentLanguage === 'ar' ? 'قياسي' : 'Standard'}</option>
                  <option value="expedited">{currentLanguage === 'ar' ? 'سريع' : 'Expedited'}</option>
                  <option value="director-approval">{currentLanguage === 'ar' ? 'موافقة المدير' : 'Director Approval'}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'ملاحظات' : 'Notes'}
                </label>
                <textarea
                  value={transferData.notes}
                  onChange={(e) => setTransferData({ ...transferData, notes: e.target.value })}
                  placeholder={currentLanguage === 'ar' ? 'أضف أي ملاحظات إضافية...' : 'Add any additional notes...'}
                  rows={4}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                />
              </div>
            </div>
          )}

          {/* Step 4: Review & Confirm */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-text-primary">
                {currentLanguage === 'ar' ? 'مراجعة التحويل' : 'Review Transfer'}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'تفاصيل المسار' : 'Route Details'}
                    </h4>
                    <div className="p-4 bg-background-secondary rounded-lg">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className="text-sm text-text-primary">
                          {warehouses.find(w => w.id === transferData.originWarehouse)?.name}
                        </span>
                        <Icon name="ArrowRight" size={14} className="text-text-muted rtl:rotate-180" />
                        <span className="text-sm text-text-primary">
                          {warehouses.find(w => w.id === transferData.destinationWarehouse)?.name}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'ملخص التحويل' : 'Transfer Summary'}
                    </h4>
                    <div className="p-4 bg-background-secondary rounded-lg space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'إجمالي المنتجات:' : 'Total Products:'}</span>
                        <span className="text-sm font-medium text-text-primary">{transferData.products.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'إجمالي الوحدات:' : 'Total Units:'}</span>
                        <span className="text-sm font-medium text-text-primary">{getTotalQuantity()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'إجمالي القيمة:' : 'Total Value:'}</span>
                        <span className="text-sm font-medium text-text-primary">
                          {getTotalValue().toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-text-secondary mb-2">
                    {currentLanguage === 'ar' ? 'المنتجات المحددة' : 'Selected Products'}
                  </h4>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {transferData.products.map((product) => (
                      <div key={product.id} className="flex items-center justify-between p-3 bg-background-secondary rounded-lg">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-text-primary">{product.name}</div>
                          <div className="text-xs text-text-muted">
                            {product.quantity} {currentLanguage === 'ar' ? 'وحدة' : 'units'} × {product.price.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                          </div>
                        </div>
                        <div className="text-sm font-medium text-text-primary">
                          {product.totalValue.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-border">
          <Button
            variant="outline"
            onClick={currentStep === 1 ? onClose : handlePrevious}
            iconName={currentStep === 1 ? "X" : "ChevronLeft"}
            iconPosition="left"
          >
            {currentStep === 1 ? (currentLanguage === 'ar' ? 'إلغاء' : 'Cancel') : (currentLanguage === 'ar' ? 'السابق' : 'Previous')}
          </Button>

          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            {currentStep < 4 ? (
              <Button
                variant="primary"
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && (!transferData.originWarehouse || !transferData.destinationWarehouse)) ||
                  (currentStep === 2 && transferData.products.length === 0)
                }
                iconName="ChevronRight"
                iconPosition="right"
              >
                {currentLanguage === 'ar' ? 'التالي' : 'Next'}
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={handleSubmit}
                iconName="Check"
                iconPosition="left"
              >
                {currentLanguage === 'ar' ? 'إنشاء التحويل' : 'Create Transfer'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InitiateTransferModal;