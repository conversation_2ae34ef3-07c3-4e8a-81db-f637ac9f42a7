import React, { useState, useEffect } from 'react';
import HeaderNavigation from '../../components/ui/HeaderNavigation';
import ContextualSidebar from '../../components/ui/ContextualSidebar';
import BreadcrumbNavigation from '../../components/ui/BreadcrumbNavigation';
import WarehouseListSidebar from './components/WarehouseListSidebar';
import WarehouseDetailsCenter from './components/WarehouseDetailsCenter';
import ActionsSidebar from './components/ActionsSidebar';
import Button from '../../components/ui/Button';
import Icon from '../../components/AppIcon';

const WarehouseManagement = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('list'); // For mobile tabs

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const warehouses = [
    {
      id: 'wh-001',
      name: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      location: currentLanguage === 'ar' ? 'الرياض، المملكة العربية السعودية' : 'Riyadh, Saudi Arabia',
      governorate: 'riyadh',
      capacity: 85,
      status: 'active',
      type: 'main',
      totalProducts: 2847,
      totalValue: 8750000.00,
      todayMovements: 156,
      pendingTransfers: 23,
      pendingOrders: 12
    },
    {
      id: 'wh-002',
      name: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
      location: currentLanguage === 'ar' ? 'الدمام، المنطقة الشرقية' : 'Dammam, Eastern Province',
      governorate: 'eastern',
      capacity: 72,
      status: 'active',
      type: 'regional',
      totalProducts: 1923,
      totalValue: 5420000.00,
      todayMovements: 89,
      pendingTransfers: 15,
      pendingOrders: 8
    },
    {
      id: 'wh-003',
      name: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
      location: currentLanguage === 'ar' ? 'جدة، مكة المكرمة' : 'Jeddah, Makkah',
      governorate: 'makkah',
      capacity: 68,
      status: 'active',
      type: 'regional',
      totalProducts: 2156,
      totalValue: 6890000.00,
      todayMovements: 134,
      pendingTransfers: 19,
      pendingOrders: 6
    },
    {
      id: 'wh-004',
      name: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk',
      location: currentLanguage === 'ar' ? 'تبوك، المملكة العربية السعودية' : 'Tabuk, Saudi Arabia',
      governorate: 'tabuk',
      capacity: 45,
      status: 'maintenance',
      type: 'regional',
      totalProducts: 1234,
      totalValue: 3450000.00,
      todayMovements: 23,
      pendingTransfers: 7,
      pendingOrders: 3
    },
    {
      id: 'wh-005',
      name: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
      location: currentLanguage === 'ar' ? 'أبها، عسير' : 'Abha, Asir',
      governorate: 'asir',
      capacity: 58,
      status: 'active',
      type: 'regional',
      totalProducts: 1678,
      totalValue: 4230000.00,
      todayMovements: 67,
      pendingTransfers: 11,
      pendingOrders: 4
    }
  ];

  useEffect(() => {
    if (!selectedWarehouse && warehouses.length > 0) {
      setSelectedWarehouse(warehouses[0]);
    }
  }, [warehouses, selectedWarehouse]);

  const handleWarehouseSelect = (warehouse) => {
    setSelectedWarehouse(warehouse);
    // On mobile, switch to details tab when warehouse is selected
    if (window.innerWidth < 768) {
      setActiveTab('details');
    }
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const mobileTabItems = [
    {
      id: 'list',
      label: currentLanguage === 'ar' ? 'القائمة' : 'List',
      icon: 'List'
    },
    {
      id: 'details',
      label: currentLanguage === 'ar' ? 'التفاصيل' : 'Details',
      icon: 'Building2'
    },
    {
      id: 'actions',
      label: currentLanguage === 'ar' ? 'الإجراءات' : 'Actions',
      icon: 'Settings'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header Navigation */}
      <HeaderNavigation />

      {/* Contextual Sidebar */}
      <ContextualSidebar 
        isOpen={isSidebarOpen} 
        onToggle={toggleSidebar}
      />

      {/* Main Content */}
      <div className="pt-16">
        {/* Breadcrumb */}
        <div className="bg-surface border-b border-border px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <BreadcrumbNavigation warehouseContext={selectedWarehouse} />
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Button
                variant="ghost"
                onClick={toggleSidebar}
                className="lg:hidden"
                iconName="Menu"
              >
                {currentLanguage === 'ar' ? 'الأدوات' : 'Tools'}
              </Button>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:flex h-[calc(100vh-8rem)]">
          {/* Left Sidebar - Warehouse List */}
          <div className="w-80 flex-shrink-0">
            <WarehouseListSidebar
              warehouses={warehouses}
              selectedWarehouse={selectedWarehouse}
              onWarehouseSelect={handleWarehouseSelect}
              currentLanguage={currentLanguage}
            />
          </div>

          {/* Center - Warehouse Details */}
          <div className="flex-1">
            <WarehouseDetailsCenter
              warehouse={selectedWarehouse}
              currentLanguage={currentLanguage}
            />
          </div>

          {/* Right Sidebar - Actions */}
          <div className="w-80 flex-shrink-0">
            <ActionsSidebar
              warehouse={selectedWarehouse}
              currentLanguage={currentLanguage}
            />
          </div>
        </div>

        {/* Tablet Layout */}
        <div className="hidden md:flex lg:hidden h-[calc(100vh-8rem)]">
          {/* Left Sidebar - Warehouse List */}
          <div className="w-80 flex-shrink-0">
            <WarehouseListSidebar
              warehouses={warehouses}
              selectedWarehouse={selectedWarehouse}
              onWarehouseSelect={handleWarehouseSelect}
              currentLanguage={currentLanguage}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Center - Warehouse Details */}
            <div className="flex-1">
              <WarehouseDetailsCenter
                warehouse={selectedWarehouse}
                currentLanguage={currentLanguage}
              />
            </div>

            {/* Bottom - Actions */}
            <div className="h-64 border-t border-border">
              <ActionsSidebar
                warehouse={selectedWarehouse}
                currentLanguage={currentLanguage}
              />
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden">
          {/* Mobile Tabs */}
          <div className="bg-surface border-b border-border">
            <div className="flex">
              {mobileTabItems.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 rtl:space-x-reverse py-3 px-4 text-sm font-medium transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'text-primary border-b-2 border-primary bg-primary-50' :'text-text-secondary hover:text-primary'
                  }`}
                >
                  <Icon name={tab.icon} size={16} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Content */}
          <div className="h-[calc(100vh-12rem)] overflow-hidden">
            {activeTab === 'list' && (
              <WarehouseListSidebar
                warehouses={warehouses}
                selectedWarehouse={selectedWarehouse}
                onWarehouseSelect={handleWarehouseSelect}
                currentLanguage={currentLanguage}
              />
            )}
            {activeTab === 'details' && (
              <WarehouseDetailsCenter
                warehouse={selectedWarehouse}
                currentLanguage={currentLanguage}
              />
            )}
            {activeTab === 'actions' && (
              <ActionsSidebar
                warehouse={selectedWarehouse}
                currentLanguage={currentLanguage}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarehouseManagement;