import React, { useState, useEffect, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import HeaderNavigation from '../../components/ui/HeaderNavigation';
import ContextualSidebar from '../../components/ui/ContextualSidebar';
import BreadcrumbNavigation from '../../components/ui/BreadcrumbNavigation';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import MovementFilters from './components/MovementFilters';
import MovementTable from './components/MovementTable';
import MovementModal from './components/MovementModal';
import MovementSummary from './components/MovementSummary';

const ProductMovementTracking = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [isFiltersCollapsed, setIsFiltersCollapsed] = useState(false);
  const [expandedRows, setExpandedRows] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState('entry');
  const [filters, setFilters] = useState({});
  const [movements, setMovements] = useState([]);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  // Mock data for product movements
  useEffect(() => {
    const mockMovements = [
      {
        id: 'mov-001',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        productName: currentLanguage === 'ar' ? 'لابتوب ديل XPS 13' : 'Dell XPS 13 Laptop',
        productSku: 'DELL-XPS13-001',
        movementType: 'entry',
        quantity: 25,
        unitPrice: 4500.00,
        totalValue: 112500.00,
        warehouse: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
        user: currentLanguage === 'ar' ? 'أحمد محمد' : 'Ahmed Mohammed',
        referenceId: 'REF-2024-001',
        category: currentLanguage === 'ar' ? 'إلكترونيات' : 'Electronics',
        location: 'A-01-15',
        notes: currentLanguage === 'ar' ? 'استلام من المورد الرئيسي' : 'Received from main supplier',
        attachments: [
          { name: 'invoice_001.pdf', type: 'pdf' },
          { name: 'delivery_receipt.jpg', type: 'image' }
        ]
      },
      {
        id: 'mov-002',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        productName: currentLanguage === 'ar' ? 'قميص قطني أزرق' : 'Blue Cotton Shirt',
        productSku: 'SHIRT-BLUE-M',
        movementType: 'exit',
        quantity: 15,
        unitPrice: 85.00,
        totalValue: 1275.00,
        warehouse: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
        user: currentLanguage === 'ar' ? 'فاطمة علي' : 'Fatima Ali',
        referenceId: 'REF-2024-002',
        category: currentLanguage === 'ar' ? 'ملابس' : 'Clothing',
        location: 'B-03-22',
        notes: currentLanguage === 'ar' ? 'شحن لمتجر التجزئة' : 'Shipped to retail store'
      },
      {
        id: 'mov-003',
        timestamp: new Date(Date.now() - 10800000).toISOString(),
        productName: currentLanguage === 'ar' ? 'زيت زيتون عضوي' : 'Organic Olive Oil',
        productSku: 'OIL-OLIVE-500ML',
        movementType: 'transfer',
        quantity: 100,
        unitPrice: 45.00,
        totalValue: 4500.00,
        warehouse: currentLanguage === 'ar' ? 'من: الرياض إلى: الدمام' : 'From: Riyadh To: Dammam',
        user: currentLanguage === 'ar' ? 'محمد السعد' : 'Mohammed Al-Saad',
        referenceId: 'REF-2024-003',
        category: currentLanguage === 'ar' ? 'طعام' : 'Food & Beverages',
        location: 'C-02-08',
        notes: currentLanguage === 'ar' ? 'تحويل لتلبية الطلب المحلي' : 'Transfer to meet local demand'
      },
      {
        id: 'mov-004',
        timestamp: new Date(Date.now() - 14400000).toISOString(),
        productName: currentLanguage === 'ar' ? 'إطار سيارة ميشلان' : 'Michelin Car Tire',
        productSku: 'TIRE-MICH-205-55R16',
        movementType: 'entry',
        quantity: 50,
        unitPrice: 320.00,
        totalValue: 16000.00,
        warehouse: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
        user: currentLanguage === 'ar' ? 'خالد الأحمد' : 'Khalid Al-Ahmad',
        referenceId: 'REF-2024-004',
        category: currentLanguage === 'ar' ? 'سيارات' : 'Automotive',
        location: 'D-01-05',
        notes: currentLanguage === 'ar' ? 'مخزون موسمي' : 'Seasonal stock'
      },
      {
        id: 'mov-005',
        timestamp: new Date(Date.now() - 18000000).toISOString(),
        productName: currentLanguage === 'ar' ? 'مجموعة أدوات حديقة' : 'Garden Tools Set',
        productSku: 'GARDEN-TOOLS-SET-01',
        movementType: 'exit',
        quantity: 8,
        unitPrice: 150.00,
        totalValue: 1200.00,
        warehouse: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
        user: currentLanguage === 'ar' ? 'نورا الزهراني' : 'Nora Al-Zahrani',
        referenceId: 'REF-2024-005',
        category: currentLanguage === 'ar' ? 'منزل وحديقة' : 'Home & Garden',
        location: 'E-02-12',
        notes: currentLanguage === 'ar' ? 'طلب عميل مباشر' : 'Direct customer order'
      }
    ];

    setMovements(mockMovements);
  }, [currentLanguage]);

  const handleWarehouseChange = (warehouse) => {
    setSelectedWarehouse(warehouse);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleRowExpand = (movementId) => {
    setExpandedRows(prev => 
      prev.includes(movementId) 
        ? prev.filter(id => id !== movementId)
        : [...prev, movementId]
    );
  };

  const handleAddMovement = (type) => {
    setModalType(type);
    setIsModalOpen(true);
  };

  const handleModalSubmit = (movementData) => {
    const newMovement = {
      id: `mov-${Date.now()}`,
      ...movementData,
      user: currentLanguage === 'ar' ? 'المستخدم الحالي' : 'Current User',
      referenceId: `REF-${new Date().getFullYear()}-${String(movements.length + 1).padStart(3, '0')}`,
      category: currentLanguage === 'ar' ? 'عام' : 'General',
      location: 'TBD'
    };

    setMovements(prev => [newMovement, ...prev]);
    console.log('New movement added:', newMovement);
  };

  const handleExportData = () => {
    console.log('Exporting movement data...');
    // Implementation for data export
  };

  const filteredMovements = React.useMemo(() => {
    let filtered = movements;

    // Filter by tab
    if (activeTab !== 'all') {
      filtered = filtered.filter(movement => movement.movementType === activeTab);
    }

    // Apply search filters
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(movement =>
        movement.productName.toLowerCase().includes(searchTerm) ||
        movement.productSku.toLowerCase().includes(searchTerm) ||
        movement.user.toLowerCase().includes(searchTerm) ||
        (movement.notes && movement.notes.toLowerCase().includes(searchTerm))
      );
    }

    // Apply date filters
    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(movement => new Date(movement.timestamp) >= fromDate);
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999);
      filtered = filtered.filter(movement => new Date(movement.timestamp) <= toDate);
    }

    // Apply other filters
    if (filters.warehouse) {
      filtered = filtered.filter(movement => 
        movement.warehouse.includes(filters.warehouse) ||
        (movement.movementType === 'transfer' && movement.warehouse.includes(filters.warehouse))
      );
    }

    if (filters.movementType) {
      filtered = filtered.filter(movement => movement.movementType === filters.movementType);
    }

    if (filters.minValue) {
      filtered = filtered.filter(movement => movement.totalValue >= parseFloat(filters.minValue));
    }

    if (filters.maxValue) {
      filtered = filtered.filter(movement => movement.totalValue <= parseFloat(filters.maxValue));
    }

    return filtered;
  }, [movements, activeTab, filters]);

  const tabs = [
    {
      id: 'all',
      label: currentLanguage === 'ar' ? 'جميع الحركات' : 'All Movements',
      icon: 'Activity',
      count: movements.length
    },
    {
      id: 'entry',
      label: currentLanguage === 'ar' ? 'الدخول' : 'Entries',
      icon: 'ArrowDown',
      count: movements.filter(m => m.movementType === 'entry').length
    },
    {
      id: 'exit',
      label: currentLanguage === 'ar' ? 'الخروج' : 'Exits',
      icon: 'ArrowUp',
      count: movements.filter(m => m.movementType === 'exit').length
    },
    {
      id: 'transfer',
      label: currentLanguage === 'ar' ? 'التحويلات' : 'Transfers',
      icon: 'ArrowRightLeft',
      count: movements.filter(m => m.movementType === 'transfer').length
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Helmet>
        <title>
          {currentLanguage === 'ar' ? 'تتبع حركة المنتجات - مدير المستودعات' : 'Product Movement Tracking - Warehouse Manager'}
        </title>
        <meta 
          name="description" 
          content={currentLanguage === 'ar' ?'تتبع حركة المنتجات مع حسابات الأسعار التلقائية وتسجيل مفصل للحركات' :'Track product movements with automatic price calculations and detailed movement logging'
          } 
        />
      </Helmet>

      <HeaderNavigation />
      
      <div className="flex">
        <ContextualSidebar 
          isOpen={isSidebarOpen}
          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}
        />
        
        <main className="flex-1 lg:ml-80">
          <div className="p-6 space-y-6">
            {/* Header Section */}
            <div className="space-y-4">
              <BreadcrumbNavigation warehouseContext={selectedWarehouse} />
              
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                  <h1 className="text-3xl font-bold text-text-primary">
                    {currentLanguage === 'ar' ? 'تتبع حركة المنتجات' : 'Product Movement Tracking'}
                  </h1>
                  <p className="text-text-secondary mt-2">
                    {currentLanguage === 'ar' ?'مراقبة وإدارة حركة المنتجات مع حسابات الأسعار التلقائية' :'Monitor and manage product movements with automatic price calculations'
                    }
                  </p>
                </div>
                
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Button
                    variant="outline"
                    onClick={handleExportData}
                    iconName="Download"
                    iconPosition="left"
                  >
                    {currentLanguage === 'ar' ? 'تصدير' : 'Export'}
                  </Button>
                  
                  <div className="relative">
                    <Button
                      variant="primary"
                      onClick={() => handleAddMovement('entry')}
                      iconName="Plus"
                      iconPosition="left"
                      className="lg:hidden"
                    >
                      {currentLanguage === 'ar' ? 'إضافة' : 'Add'}
                    </Button>
                    
                    <div className="hidden lg:flex items-center space-x-2 rtl:space-x-reverse">
                      <Button
                        variant="primary"
                        onClick={() => handleAddMovement('entry')}
                        iconName="ArrowDown"
                        iconPosition="left"
                      >
                        {currentLanguage === 'ar' ? 'إضافة دخول' : 'Add Entry'}
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={() => handleAddMovement('exit')}
                        iconName="ArrowUp"
                        iconPosition="left"
                      >
                        {currentLanguage === 'ar' ? 'إضافة خروج' : 'Add Exit'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleAddMovement('transfer')}
                        iconName="ArrowRightLeft"
                        iconPosition="left"
                      >
                        {currentLanguage === 'ar' ? 'إضافة تحويل' : 'Add Transfer'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Summary Section */}
            <MovementSummary 
              movements={filteredMovements}
              selectedTab={activeTab}
            />

            {/* Filters Section */}
            <MovementFilters
              onFiltersChange={handleFiltersChange}
              isCollapsed={isFiltersCollapsed}
              onToggleCollapse={() => setIsFiltersCollapsed(!isFiltersCollapsed)}
            />

            {/* Tabs Section */}
            <div className="bg-surface border border-border rounded-lg shadow-elevation-1">
              <div className="border-b border-border">
                <nav className="flex space-x-8 rtl:space-x-reverse px-6" aria-label="Tabs">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => handleTabChange(tab.id)}
                      className={`flex items-center space-x-2 rtl:space-x-reverse py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                        activeTab === tab.id
                          ? 'border-primary text-primary' :'border-transparent text-text-secondary hover:text-text-primary hover:border-secondary-300'
                      }`}
                    >
                      <Icon name={tab.icon} size={16} />
                      <span>{tab.label}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        activeTab === tab.id
                          ? 'bg-primary-100 text-primary' :'bg-secondary-100 text-text-secondary'
                      }`}>
                        {tab.count}
                      </span>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Table Section */}
              <div className="p-6">
                <MovementTable
                  movements={filteredMovements}
                  onRowExpand={handleRowExpand}
                  expandedRows={expandedRows}
                />
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* Movement Modal */}
      <MovementModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        movementType={modalType}
        onSubmit={handleModalSubmit}
      />

      {/* Mobile Add Movement Menu */}
      <div className="lg:hidden fixed bottom-6 right-6 z-40">
        <div className="flex flex-col space-y-3">
          <Button
            variant="primary"
            onClick={() => handleAddMovement('entry')}
            className="w-14 h-14 rounded-full shadow-elevation-3"
            iconName="ArrowDown"
          />
          <Button
            variant="secondary"
            onClick={() => handleAddMovement('exit')}
            className="w-14 h-14 rounded-full shadow-elevation-3"
            iconName="ArrowUp"
          />
          <Button
            variant="outline"
            onClick={() => handleAddMovement('transfer')}
            className="w-14 h-14 rounded-full shadow-elevation-3 bg-surface"
            iconName="ArrowRightLeft"
          />
        </div>
      </div>

      {/* Sidebar Toggle Button */}
      <Button
        variant="primary"
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        className="fixed top-20 left-4 z-40 lg:hidden w-12 h-12 rounded-full shadow-elevation-2"
        iconName="Menu"
      />
    </div>
  );
};

export default ProductMovementTracking;