import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ActionsSidebar = ({ warehouse, currentLanguage }) => {
  const [notifications] = useState([
    {
      id: 1,
      type: 'warning',
      title: currentLanguage === 'ar' ? 'مخزون منخفض' : 'Low Stock Alert',
      message: currentLanguage === 'ar' ? 'شاشة سامسونج 27 بوصة - 8 وحدات متبقية' : 'Samsung 27" Monitor - 8 units remaining',
      timestamp: '2024-01-15T10:30:00',
      priority: 'high'
    },
    {
      id: 2,
      type: 'success',
      title: currentLanguage === 'ar' ? 'تم الاستلام' : 'Received',
      message: currentLanguage === 'ar' ? 'تم استلام 50 وحدة من أجهزة الكمبيوتر المحمولة' : 'Received 50 units of Dell Laptops',
      timestamp: '2024-01-15T09:15:00',
      priority: 'medium'
    },
    {
      id: 3,
      type: 'info',
      title: currentLanguage === 'ar' ? 'تحويل مكتمل' : 'Transfer Complete',
      message: currentLanguage === 'ar' ? 'تم تحويل 25 كرسي مكتب إلى مستودع جدة' : 'Transferred 25 office chairs to Jeddah warehouse',
      timestamp: '2024-01-15T08:45:00',
      priority: 'low'
    }
  ]);

  const quickActions = [
    {
      id: 'add-product',
      label: currentLanguage === 'ar' ? 'إضافة منتج' : 'Add Product',
      icon: 'Plus',
      variant: 'primary',
      description: currentLanguage === 'ar' ? 'إضافة منتج جديد إلى المخزون' : 'Add new product to inventory'
    },
    {
      id: 'initiate-transfer',
      label: currentLanguage === 'ar' ? 'بدء تحويل' : 'Initiate Transfer',
      icon: 'ArrowRightLeft',
      variant: 'secondary',
      description: currentLanguage === 'ar' ? 'تحويل منتجات إلى مستودع آخر' : 'Transfer products to another warehouse'
    },
    {
      id: 'view-analytics',
      label: currentLanguage === 'ar' ? 'عرض التحليلات' : 'View Analytics',
      icon: 'BarChart3',
      variant: 'outline',
      description: currentLanguage === 'ar' ? 'عرض تحليلات مفصلة للمستودع' : 'View detailed warehouse analytics'
    },
    {
      id: 'generate-report',
      label: currentLanguage === 'ar' ? 'إنشاء تقرير' : 'Generate Report',
      icon: 'FileText',
      variant: 'outline',
      description: currentLanguage === 'ar' ? 'إنشاء تقرير شامل للمستودع' : 'Generate comprehensive warehouse report'
    },
    {
      id: 'bulk-update',
      label: currentLanguage === 'ar' ? 'تحديث مجمع' : 'Bulk Update',
      icon: 'Upload',
      variant: 'ghost',
      description: currentLanguage === 'ar' ? 'تحديث عدة منتجات في نفس الوقت' : 'Update multiple products at once'
    },
    {
      id: 'export-data',
      label: currentLanguage === 'ar' ? 'تصدير البيانات' : 'Export Data',
      icon: 'Download',
      variant: 'ghost',
      description: currentLanguage === 'ar' ? 'تصدير بيانات المستودع' : 'Export warehouse data'
    }
  ];

  const quickStats = warehouse ? [
    {
      label: currentLanguage === 'ar' ? 'الحركات اليوم' : 'Today\'s Movements',
      value: warehouse.todayMovements || 0,
      icon: 'TruckIcon',
      trend: '+12%',
      trendDirection: 'up'
    },
    {
      label: currentLanguage === 'ar' ? 'التحويلات المعلقة' : 'Pending Transfers',
      value: warehouse.pendingTransfers || 0,
      icon: 'Clock',
      trend: '-5%',
      trendDirection: 'down'
    },
    {
      label: currentLanguage === 'ar' ? 'التنبيهات النشطة' : 'Active Alerts',
      value: notifications.filter(n => n.priority === 'high').length,
      icon: 'AlertTriangle',
      trend: '+2',
      trendDirection: 'up'
    },
    {
      label: currentLanguage === 'ar' ? 'الطلبات المعلقة' : 'Pending Orders',
      value: warehouse.pendingOrders || 0,
      icon: 'ShoppingCart',
      trend: '+8%',
      trendDirection: 'up'
    }
  ] : [];

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'warning':
        return 'AlertTriangle';
      case 'success':
        return 'CheckCircle';
      case 'info':
        return 'Info';
      case 'error':
        return 'XCircle';
      default:
        return 'Bell';
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'warning':
        return 'text-warning bg-warning-50';
      case 'success':
        return 'text-success bg-success-50';
      case 'info':
        return 'text-primary bg-primary-50';
      case 'error':
        return 'text-error bg-error-50';
      default:
        return 'text-text-muted bg-secondary-50';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const handleAction = (actionId) => {
    console.log(`Executing action: ${actionId}`);
    // Here you would implement the actual action logic
  };

  return (
    <div className="h-full flex flex-col bg-surface border-l border-border">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <h2 className="text-lg font-semibold text-text-primary">
          {currentLanguage === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
        </h2>
      </div>

      {/* Quick Stats */}
      {warehouse && (
        <div className="p-6 border-b border-border">
          <h3 className="text-sm font-medium text-text-secondary mb-4">
            {currentLanguage === 'ar' ? 'إحصائيات سريعة' : 'Quick Stats'}
          </h3>
          <div className="space-y-4">
            {quickStats.map((stat, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center">
                    <Icon name={stat.icon} size={16} color="var(--color-primary)" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-text-primary">{stat.value}</p>
                    <p className="text-xs text-text-secondary">{stat.label}</p>
                  </div>
                </div>
                <div className={`text-xs font-medium ${
                  stat.trendDirection === 'up' ? 'text-success' : 'text-error'
                }`}>
                  {stat.trend}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="p-6 border-b border-border">
        <h3 className="text-sm font-medium text-text-secondary mb-4">
          {currentLanguage === 'ar' ? 'الإجراءات' : 'Actions'}
        </h3>
        <div className="space-y-3">
          {quickActions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant}
              onClick={() => handleAction(action.id)}
              className="w-full justify-start text-left"
              iconName={action.icon}
              iconPosition="left"
              disabled={!warehouse}
            >
              <div className="flex flex-col items-start">
                <span className="font-medium">{action.label}</span>
                <span className="text-xs opacity-75 font-normal">
                  {action.description}
                </span>
              </div>
            </Button>
          ))}
        </div>
      </div>

      {/* Notifications */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-text-secondary">
              {currentLanguage === 'ar' ? 'التنبيهات الأخيرة' : 'Recent Notifications'}
            </h3>
            <Button variant="ghost" className="p-1">
              <Icon name="MoreVertical" size={16} />
            </Button>
          </div>
          
          <div className="space-y-3">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className="p-3 rounded-lg border border-border hover:bg-surface-secondary transition-colors duration-150 cursor-pointer"
              >
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${getNotificationColor(notification.type)}`}>
                    <Icon name={getNotificationIcon(notification.type)} size={16} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-sm font-medium text-text-primary truncate">
                        {notification.title}
                      </h4>
                      <span className="text-xs text-text-muted flex-shrink-0 ml-2 rtl:mr-2 rtl:ml-0">
                        {formatDate(notification.timestamp)}
                      </span>
                    </div>
                    <p className="text-xs text-text-secondary line-clamp-2">
                      {notification.message}
                    </p>
                    {notification.priority === 'high' && (
                      <div className="mt-2">
                        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-error-50 text-error">
                          {currentLanguage === 'ar' ? 'عالي الأولوية' : 'High Priority'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4">
            <Button variant="ghost" className="w-full">
              {currentLanguage === 'ar' ? 'عرض جميع التنبيهات' : 'View All Notifications'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActionsSidebar;