import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';


const WarehouseListSidebar = ({ warehouses, selectedWarehouse, onWarehouseSelect, currentLanguage }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGovernorate, setSelectedGovernorate] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const governorates = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع المحافظات' : 'All Governorates' },
    { value: 'riyadh', label: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh' },
    { value: 'eastern', label: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province' },
    { value: 'makkah', label: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah' },
    { value: 'tabuk', label: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk' },
    { value: 'asir', label: currentLanguage === 'ar' ? 'عسير' : 'Asir' }
  ];

  const statusOptions = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع الحالات' : 'All Status' },
    { value: 'active', label: currentLanguage === 'ar' ? 'نشط' : 'Active' },
    { value: 'maintenance', label: currentLanguage === 'ar' ? 'صيانة' : 'Maintenance' },
    { value: 'inactive', label: currentLanguage === 'ar' ? 'غير نشط' : 'Inactive' }
  ];

  const filteredWarehouses = warehouses.filter(warehouse => {
    const matchesSearch = warehouse.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         warehouse.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGovernorate = selectedGovernorate === 'all' || warehouse.governorate === selectedGovernorate;
    const matchesStatus = selectedStatus === 'all' || warehouse.status === selectedStatus;
    
    return matchesSearch && matchesGovernorate && matchesStatus;
  });

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'text-success';
      case 'maintenance':
        return 'text-warning';
      case 'inactive':
        return 'text-error';
      default:
        return 'text-text-muted';
    }
  };

  const getCapacityColor = (capacity) => {
    if (capacity >= 90) return 'bg-error';
    if (capacity >= 75) return 'bg-warning';
    return 'bg-success';
  };

  const getCapacityTextColor = (capacity) => {
    if (capacity >= 90) return 'text-error';
    if (capacity >= 75) return 'text-warning';
    return 'text-success';
  };

  return (
    <div className="h-full flex flex-col bg-surface border-r border-border">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h2 className="text-lg font-semibold text-text-primary mb-4">
          {currentLanguage === 'ar' ? 'المستودعات' : 'Warehouses'}
        </h2>
        
        {/* Search */}
        <div className="relative mb-4">
          <Icon 
            name="Search" 
            size={16} 
            className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-text-muted" 
          />
          <Input
            type="search"
            placeholder={currentLanguage === 'ar' ? 'البحث في المستودعات...' : 'Search warehouses...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rtl:pr-10 rtl:pl-3"
          />
        </div>

        {/* Filters */}
        <div className="space-y-3">
          <div>
            <label className="block text-xs font-medium text-text-secondary mb-1">
              {currentLanguage === 'ar' ? 'المحافظة' : 'Governorate'}
            </label>
            <select
              value={selectedGovernorate}
              onChange={(e) => setSelectedGovernorate(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {governorates.map(gov => (
                <option key={gov.value} value={gov.value}>{gov.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-xs font-medium text-text-secondary mb-1">
              {currentLanguage === 'ar' ? 'الحالة' : 'Status'}
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Warehouse List */}
      <div className="flex-1 overflow-y-auto">
        {filteredWarehouses.length === 0 ? (
          <div className="p-4 text-center">
            <Icon name="Search" size={32} className="mx-auto mb-2 text-text-muted opacity-50" />
            <p className="text-sm text-text-muted">
              {currentLanguage === 'ar' ? 'لم يتم العثور على مستودعات' : 'No warehouses found'}
            </p>
          </div>
        ) : (
          <div className="p-2">
            {filteredWarehouses.map((warehouse) => (
              <button
                key={warehouse.id}
                onClick={() => onWarehouseSelect(warehouse)}
                className={`w-full p-3 mb-2 rounded-lg text-left transition-all duration-200 ${
                  selectedWarehouse?.id === warehouse.id
                    ? 'bg-primary-50 border-2 border-primary' :'bg-surface-secondary hover:bg-primary-50 border-2 border-transparent'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon 
                        name={warehouse.type === 'main' ? 'Building2' : 'Warehouse'} 
                        size={16} 
                        color="var(--color-primary)" 
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-text-primary truncate">
                        {warehouse.name}
                      </h3>
                      <p className="text-xs text-text-secondary">
                        {warehouse.location}
                      </p>
                    </div>
                  </div>
                  <div className={`text-xs font-medium ${getStatusColor(warehouse.status)}`}>
                    {warehouse.status === 'active' ? (currentLanguage === 'ar' ? 'نشط' : 'Active') :
                     warehouse.status === 'maintenance' ? (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance') :
                     (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className={`text-xs font-medium ${getCapacityTextColor(warehouse.capacity)}`}>
                      {warehouse.capacity}%
                    </span>
                    <div className="w-16 h-1.5 bg-secondary-200 rounded-full overflow-hidden">
                      <div 
                        className={`h-full rounded-full transition-all duration-300 ${getCapacityColor(warehouse.capacity)}`}
                        style={{ width: `${warehouse.capacity}%` }}
                      />
                    </div>
                  </div>
                  <div className="text-xs text-text-muted">
                    {warehouse.totalProducts} {currentLanguage === 'ar' ? 'منتج' : 'items'}
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WarehouseListSidebar;