import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';

const WarehouseCapacityChart = ({ currentLanguage }) => {
  // Mock warehouse capacity data
  const capacityData = [
    {
      warehouse: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh',
      capacity: 85,
      used: 72,
      available: 13,
      products: 2847
    },
    {
      warehouse: currentLanguage === 'ar' ? 'جدة' : 'Jeddah',
      capacity: 78,
      used: 65,
      available: 13,
      products: 1923
    },
    {
      warehouse: currentLanguage === 'ar' ? 'الدمام' : 'Dammam',
      capacity: 72,
      used: 58,
      available: 14,
      products: 1756
    },
    {
      warehouse: currentLanguage === 'ar' ? 'أبها' : 'Abha',
      capacity: 68,
      used: 45,
      available: 23,
      products: 1234
    },
    {
      warehouse: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk',
      capacity: 58,
      used: 35,
      available: 23,
      products: 892
    }
  ];

  const getCapacityColor = (percentage) => {
    if (percentage >= 80) return '#EF4444'; // Red for high capacity
    if (percentage >= 60) return '#F59E0B'; // Yellow for medium capacity
    return '#10B981'; // Green for low capacity
  };

  const getCapacityStatus = (percentage) => {
    if (percentage >= 80) {
      return currentLanguage === 'ar' ? 'مرتفع' : 'High';
    }
    if (percentage >= 60) {
      return currentLanguage === 'ar' ? 'متوسط' : 'Medium';
    }
    return currentLanguage === 'ar' ? 'منخفض' : 'Low';
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-surface border border-border rounded-lg p-4 shadow-lg">
          <p className="text-text-primary font-medium mb-2">{label}</p>
          <div className="space-y-1 text-sm">
            <p className="text-text-secondary">
              {currentLanguage === 'ar' ? 'السعة المستخدمة:' : 'Used Capacity:'} {data.used}%
            </p>
            <p className="text-text-secondary">
              {currentLanguage === 'ar' ? 'السعة المتاحة:' : 'Available Capacity:'} {data.available}%
            </p>
            <p className="text-text-secondary">
              {currentLanguage === 'ar' ? 'عدد المنتجات:' : 'Products Count:'} {data.products?.toLocaleString()}
            </p>
            <p className={`font-medium ${data.used >= 80 ? 'text-error' : data.used >= 60 ? 'text-warning' : 'text-success'}`}>
              {currentLanguage === 'ar' ? 'الحالة:' : 'Status:'} {getCapacityStatus(data.used)}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Capacity Chart */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'تحليل سعة المستودعات' : 'Warehouse Capacity Analysis'}
          </h3>
          <div className="text-sm text-text-secondary">
            {currentLanguage === 'ar' ? 'النسبة المئوية للاستخدام' : 'Utilization Percentage'}
          </div>
        </div>

        <div className="h-80 mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={capacityData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis 
                dataKey="warehouse" 
                stroke="#6B7280"
                fontSize={12}
                tick={{ fill: '#6B7280' }}
              />
              <YAxis 
                stroke="#6B7280"
                fontSize={12}
                tick={{ fill: '#6B7280' }}
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="used" radius={[4, 4, 0, 0]}>
                {capacityData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={getCapacityColor(entry.used)} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Capacity Legend */}
        <div className="flex items-center justify-center space-x-6 rtl:space-x-reverse text-sm">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-4 h-4 bg-success rounded"></div>
            <span className="text-text-secondary">
              {currentLanguage === 'ar' ? 'منخفض (< 60%)' : 'Low (< 60%)'}
            </span>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-4 h-4 bg-warning rounded"></div>
            <span className="text-text-secondary">
              {currentLanguage === 'ar' ? 'متوسط (60-80%)' : 'Medium (60-80%)'}
            </span>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-4 h-4 bg-error rounded"></div>
            <span className="text-text-secondary">
              {currentLanguage === 'ar' ? 'مرتفع (> 80%)' : 'High (> 80%)'}
            </span>
          </div>
        </div>
      </div>

      {/* Capacity Details Table */}
      <div className="bg-surface border border-border rounded-lg">
        <div className="p-6 border-b border-border">
          <h4 className="text-lg font-semibold text-text-primary">
            {currentLanguage === 'ar' ? 'تفاصيل السعة' : 'Capacity Details'}
          </h4>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-background border-b border-border">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'المستودع' : 'Warehouse'}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'السعة المستخدمة' : 'Used Capacity'}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'السعة المتاحة' : 'Available Capacity'}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'عدد المنتجات' : 'Products Count'}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'الحالة' : 'Status'}
                </th>
              </tr>
            </thead>
            <tbody className="bg-surface divide-y divide-border">
              {capacityData.map((warehouse, index) => (
                <tr key={index} className="hover:bg-surface-hover transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary">
                    {warehouse.warehouse}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-primary">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="w-16 bg-background rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${warehouse.used}%`,
                            backgroundColor: getCapacityColor(warehouse.used)
                          }}
                        />
                      </div>
                      <span className="font-medium">{warehouse.used}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-primary">
                    {warehouse.available}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-primary">
                    {warehouse.products?.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        warehouse.used >= 80
                          ? 'bg-error-100 text-error'
                          : warehouse.used >= 60
                          ? 'bg-warning-100 text-warning' :'bg-success-100 text-success'
                      }`}
                    >
                      {getCapacityStatus(warehouse.used)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default WarehouseCapacityChart;