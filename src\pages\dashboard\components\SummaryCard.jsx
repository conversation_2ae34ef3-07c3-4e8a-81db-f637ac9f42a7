import React from 'react';
import Icon from '../../../components/AppIcon';

const SummaryCard = ({ title, value, subtitle, icon, trend, trendDirection, color = 'primary' }) => {
  const getColorClasses = (colorType) => {
    const colors = {
      primary: 'bg-primary-50 text-primary border-primary-100',
      success: 'bg-success-50 text-success border-success-100',
      warning: 'bg-warning-50 text-warning border-warning-100',
      error: 'bg-error-50 text-error border-error-100'
    };
    return colors[colorType] || colors.primary;
  };

  const getTrendColor = (direction) => {
    return direction === 'up' ? 'text-success' : direction === 'down' ? 'text-error' : 'text-text-muted';
  };

  return (
    <div className="bg-surface border border-border rounded-lg p-6 shadow-elevation-1 hover:shadow-elevation-2 transition-shadow duration-200">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-text-secondary mb-1">{title}</p>
          <p className="text-2xl font-bold text-text-primary mb-2">{value}</p>
          {subtitle && (
            <p className="text-xs text-text-muted">{subtitle}</p>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(color)}`}>
          <Icon name={icon} size={24} />
        </div>
      </div>
      
      {trend && (
        <div className="flex items-center mt-4 pt-4 border-t border-border">
          <Icon 
            name={trendDirection === 'up' ? 'TrendingUp' : trendDirection === 'down' ? 'TrendingDown' : 'Minus'} 
            size={16} 
            className={getTrendColor(trendDirection)}
          />
          <span className={`text-sm font-medium ml-2 ${getTrendColor(trendDirection)}`}>
            {trend}
          </span>
          <span className="text-xs text-text-muted ml-1">vs last month</span>
        </div>
      )}
    </div>
  );
};

export default SummaryCard;