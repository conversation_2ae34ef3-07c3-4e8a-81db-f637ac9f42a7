import React from 'react';
import Icon from '../../../components/AppIcon';

const ReportSummaryCards = ({ data, currentLanguage }) => {
  const getColorClasses = (color) => {
    const colorMap = {
      primary: 'bg-primary-100 text-primary border-primary-200',
      success: 'bg-success-100 text-success border-success-200',
      warning: 'bg-warning-100 text-warning border-warning-200',
      error: 'bg-error-100 text-error border-error-200'
    };
    return colorMap[color] || colorMap.primary;
  };

  const getTrendColor = (direction) => {
    return direction === 'up' ? 'text-success' : 'text-error';
  };

  const getTrendIcon = (direction) => {
    return direction === 'up' ? 'TrendingUp' : 'TrendingDown';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {data?.map((card, index) => (
        <div key={index} className="bg-surface border border-border rounded-lg p-6 shadow-elevation-1 hover:shadow-elevation-2 transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(card.color)}`}>
              <Icon name={card.icon} size={24} />
            </div>
            {card.trend && (
              <div className={`flex items-center space-x-1 rtl:space-x-reverse ${getTrendColor(card.trendDirection)}`}>
                <Icon name={getTrendIcon(card.trendDirection)} size={16} />
                <span className="text-sm font-medium">{card.trend}</span>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <h3 className="text-2xl font-bold text-text-primary">
              {card.value}
            </h3>
            <p className="text-sm font-medium text-text-primary">
              {card.title}
            </p>
            {card.subtitle && (
              <p className="text-xs text-text-secondary">
                {card.subtitle}
              </p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ReportSummaryCards;