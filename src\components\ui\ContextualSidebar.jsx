import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';
import WarehouseSelector from './WarehouseSelector';

const ContextualSidebar = ({ isOpen, onToggle, className = "" }) => {
  const location = useLocation();
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const getContextualActions = () => {
    const basePath = location.pathname;
    
    switch (basePath) {
      case '/dashboard':
        return [
          {
            id: 'quick-inventory',
            label: currentLanguage === 'ar' ? 'جرد سريع' : 'Quick Inventory',
            icon: 'ClipboardList',
            action: () => console.log('Quick inventory'),
            variant: 'primary'
          },
          {
            id: 'add-product',
            label: currentLanguage === 'ar' ? 'إضافة منتج' : 'Add Product',
            icon: 'Plus',
            action: () => console.log('Add product'),
            variant: 'secondary'
          },
          {
            id: 'generate-report',
            label: currentLanguage === 'ar' ? 'إنشاء تقرير' : 'Generate Report',
            icon: 'FileText',
            action: () => console.log('Generate report'),
            variant: 'outline'
          }
        ];
      
      case '/warehouse-management':
        return [
          {
            id: 'add-location',
            label: currentLanguage === 'ar' ? 'إضافة موقع' : 'Add Location',
            icon: 'MapPin',
            action: () => console.log('Add location'),
            variant: 'primary'
          },
          {
            id: 'bulk-update',
            label: currentLanguage === 'ar' ? 'تحديث مجمع' : 'Bulk Update',
            icon: 'Upload',
            action: () => console.log('Bulk update'),
            variant: 'secondary'
          },
          {
            id: 'export-data',
            label: currentLanguage === 'ar' ? 'تصدير البيانات' : 'Export Data',
            icon: 'Download',
            action: () => console.log('Export data'),
            variant: 'outline'
          }
        ];
      
      case '/product-movement-tracking':
        return [
          {
            id: 'new-movement',
            label: currentLanguage === 'ar' ? 'حركة جديدة' : 'New Movement',
            icon: 'ArrowRight',
            action: () => console.log('New movement'),
            variant: 'primary'
          },
          {
            id: 'scan-barcode',
            label: currentLanguage === 'ar' ? 'مسح الباركود' : 'Scan Barcode',
            icon: 'ScanLine',
            action: () => console.log('Scan barcode'),
            variant: 'secondary'
          }
        ];
      
      case '/inter-warehouse-transfers':
        return [
          {
            id: 'new-transfer',
            label: currentLanguage === 'ar' ? 'تحويل جديد' : 'New Transfer',
            icon: 'ArrowRightLeft',
            action: () => console.log('New transfer'),
            variant: 'primary'
          },
          {
            id: 'approve-pending',
            label: currentLanguage === 'ar' ? 'الموافقة على المعلق' : 'Approve Pending',
            icon: 'CheckCircle',
            action: () => console.log('Approve pending'),
            variant: 'secondary'
          }
        ];
      
      case '/analytics-dashboard':
        return [
          {
            id: 'custom-report',
            label: currentLanguage === 'ar' ? 'تقرير مخصص' : 'Custom Report',
            icon: 'BarChart3',
            action: () => console.log('Custom report'),
            variant: 'primary'
          },
          {
            id: 'export-analytics',
            label: currentLanguage === 'ar' ? 'تصدير التحليلات' : 'Export Analytics',
            icon: 'Download',
            action: () => console.log('Export analytics'),
            variant: 'outline'
          }
        ];
      
      default:
        return [];
    }
  };

  const getQuickStats = () => {
    if (!selectedWarehouse) return [];

    return [
      {
        label: currentLanguage === 'ar' ? 'إجمالي المنتجات' : 'Total Products',
        value: '2,847',
        icon: 'Package',
        trend: '+12%',
        trendDirection: 'up'
      },
      {
        label: currentLanguage === 'ar' ? 'الحركات اليوم' : 'Today\'s Movements',
        value: '156',
        icon: 'TruckIcon',
        trend: '+8%',
        trendDirection: 'up'
      },
      {
        label: currentLanguage === 'ar' ? 'التحويلات المعلقة' : 'Pending Transfers',
        value: '23',
        icon: 'Clock',
        trend: '-5%',
        trendDirection: 'down'
      },
      {
        label: currentLanguage === 'ar' ? 'التنبيهات النشطة' : 'Active Alerts',
        value: '7',
        icon: 'AlertTriangle',
        trend: '+2',
        trendDirection: 'up'
      }
    ];
  };

  const contextualActions = getContextualActions();
  const quickStats = getQuickStats();

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <aside 
        className={`
          fixed top-16 left-0 h-[calc(100vh-4rem)] w-80 bg-surface border-r border-border shadow-elevation-1 z-50
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0 lg:static lg:h-[calc(100vh-4rem)]
          ${className}
        `}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-text-primary">
                {currentLanguage === 'ar' ? 'أدوات سريعة' : 'Quick Tools'}
              </h2>
              <Button
                variant="ghost"
                onClick={onToggle}
                className="lg:hidden p-1"
                aria-label="Close sidebar"
              >
                <Icon name="X" size={18} />
              </Button>
            </div>
            
            {/* Warehouse Selector */}
            <WarehouseSelector 
              onWarehouseChange={setSelectedWarehouse}
              className="w-full"
            />
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Quick Stats */}
            {quickStats.length > 0 && (
              <div className="p-6 border-b border-border">
                <h3 className="text-sm font-medium text-text-secondary mb-4">
                  {currentLanguage === 'ar' ? 'إحصائيات سريعة' : 'Quick Stats'}
                </h3>
                <div className="space-y-4">
                  {quickStats.map((stat) => (
                    <div key={stat.label} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="w-8 h-8 bg-primary-50 rounded-lg flex items-center justify-center">
                          <Icon name={stat.icon} size={16} color="var(--color-primary)" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-text-primary">{stat.value}</p>
                          <p className="text-xs text-text-secondary">{stat.label}</p>
                        </div>
                      </div>
                      <div className={`text-xs font-medium ${
                        stat.trendDirection === 'up' ? 'text-success' : 'text-error'
                      }`}>
                        {stat.trend}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Contextual Actions */}
            {contextualActions.length > 0 && (
              <div className="p-6">
                <h3 className="text-sm font-medium text-text-secondary mb-4">
                  {currentLanguage === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
                </h3>
                <div className="space-y-3">
                  {contextualActions.map((action) => (
                    <Button
                      key={action.id}
                      variant={action.variant}
                      onClick={action.action}
                      className="w-full justify-start"
                      iconName={action.icon}
                      iconPosition="left"
                    >
                      {action.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Activity */}
            <div className="p-6 border-t border-border">
              <h3 className="text-sm font-medium text-text-secondary mb-4">
                {currentLanguage === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}
              </h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-success-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Icon name="CheckCircle" size={12} color="var(--color-success)" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-text-primary">
                      {currentLanguage === 'ar' ? 'تم استلام 50 وحدة من المنتج A' : 'Received 50 units of Product A'}
                    </p>
                    <p className="text-xs text-text-muted">
                      {currentLanguage === 'ar' ? 'منذ 5 دقائق' : '5 minutes ago'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-warning-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Icon name="AlertTriangle" size={12} color="var(--color-warning)" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-text-primary">
                      {currentLanguage === 'ar' ? 'مخزون منخفض للمنتج B' : 'Low stock alert for Product B'}
                    </p>
                    <p className="text-xs text-text-muted">
                      {currentLanguage === 'ar' ? 'منذ 15 دقيقة' : '15 minutes ago'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Icon name="ArrowRightLeft" size={12} color="var(--color-primary)" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-text-primary">
                      {currentLanguage === 'ar' ? 'تحويل إلى مستودع جدة مكتمل' : 'Transfer to Jeddah warehouse completed'}
                    </p>
                    <p className="text-xs text-text-muted">
                      {currentLanguage === 'ar' ? 'منذ 30 دقيقة' : '30 minutes ago'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};

export default ContextualSidebar;