import React from 'react';
import Icon from '../../../components/AppIcon';

const KPICard = ({ title, value, unit, trend, trendDirection, icon, color = "primary" }) => {
  const getTrendColor = () => {
    if (trendDirection === 'up') return 'text-success';
    if (trendDirection === 'down') return 'text-error';
    return 'text-text-muted';
  };

  const getTrendIcon = () => {
    if (trendDirection === 'up') return 'TrendingUp';
    if (trendDirection === 'down') return 'TrendingDown';
    return 'Minus';
  };

  const getColorClasses = () => {
    switch (color) {
      case 'success':
        return 'bg-success-50 text-success';
      case 'warning':
        return 'bg-warning-50 text-warning';
      case 'error':
        return 'bg-error-50 text-error';
      default:
        return 'bg-primary-50 text-primary';
    }
  };

  return (
    <div className="card p-6">
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses()}`}>
          <Icon name={icon} size={24} />
        </div>
        {trend && (
          <div className={`flex items-center space-x-1 rtl:space-x-reverse ${getTrendColor()}`}>
            <Icon name={getTrendIcon()} size={16} />
            <span className="text-sm font-medium">{trend}</span>
          </div>
        )}
      </div>
      <div>
        <h3 className="text-2xl font-bold text-text-primary mb-1">
          {value}
          {unit && <span className="text-lg text-text-secondary ml-1">{unit}</span>}
        </h3>
        <p className="text-sm text-text-secondary">{title}</p>
      </div>
    </div>
  );
};

export default KPICard;