import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const TransferDetailsModal = ({ transfer, onClose, currentLanguage }) => {
  if (!transfer) return null;

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-warning-100 text-warning-700';
      case 'approved':
        return 'bg-primary-100 text-primary-700';
      case 'in-transit':
        return 'bg-accent-100 text-accent-700';
      case 'completed':
        return 'bg-success-100 text-success-700';
      case 'cancelled':
        return 'bg-error-100 text-error-700';
      default:
        return 'bg-secondary-100 text-secondary-700';
    }
  };

  const getStatusText = (status) => {
    const statusMap = {
      'pending': currentLanguage === 'ar' ? 'معلق' : 'Pending',
      'approved': currentLanguage === 'ar' ? 'موافق عليه' : 'Approved',
      'in-transit': currentLanguage === 'ar' ? 'في الطريق' : 'In Transit',
      'completed': currentLanguage === 'ar' ? 'مكتمل' : 'Completed',
      'cancelled': currentLanguage === 'ar' ? 'ملغي' : 'Cancelled'
    };
    return statusMap[status] || status;
  };

  const progressSteps = [
    { id: 'requested', label: currentLanguage === 'ar' ? 'مطلوب' : 'Requested', icon: 'FileText' },
    { id: 'approved', label: currentLanguage === 'ar' ? 'موافق عليه' : 'Approved', icon: 'CheckCircle' },
    { id: 'picked', label: currentLanguage === 'ar' ? 'تم الانتقاء' : 'Picked', icon: 'Package' },
    { id: 'in-transit', label: currentLanguage === 'ar' ? 'في الطريق' : 'In Transit', icon: 'Truck' },
    { id: 'delivered', label: currentLanguage === 'ar' ? 'تم التسليم' : 'Delivered', icon: 'MapPin' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-surface rounded-lg shadow-elevation-3 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">
              {currentLanguage === 'ar' ? 'تفاصيل التحويل' : 'Transfer Details'}
            </h2>
            <p className="text-sm text-text-muted mt-1">{transfer.id}</p>
          </div>
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(transfer.status)}`}>
              {getStatusText(transfer.status)}
            </span>
            <Button variant="ghost" onClick={onClose} className="p-2">
              <Icon name="X" size={20} />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-8rem)]">
          <div className="p-6 space-y-6">
            {/* Transfer Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium text-text-primary mb-4">
                    {currentLanguage === 'ar' ? 'معلومات التحويل' : 'Transfer Information'}
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'تاريخ الطلب:' : 'Requested Date:'}</span>
                      <span className="text-sm font-medium text-text-primary">{transfer.requestedDate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'المنسق:' : 'Coordinator:'}</span>
                      <span className="text-sm font-medium text-text-primary">{transfer.coordinator}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'الأولوية:' : 'Priority:'}</span>
                      <span className={`text-sm font-medium ${
                        transfer.priority === 'urgent' ? 'text-error' :
                        transfer.priority === 'high' ? 'text-warning' :
                        transfer.priority === 'normal' ? 'text-primary' : 'text-text-muted'
                      }`}>
                        {transfer.priority === 'urgent' ? (currentLanguage === 'ar' ? 'عاجل' : 'Urgent') :
                         transfer.priority === 'high' ? (currentLanguage === 'ar' ? 'عالي' : 'High') :
                         transfer.priority === 'normal' ? (currentLanguage === 'ar' ? 'عادي' : 'Normal') :
                         (currentLanguage === 'ar' ? 'منخفض' : 'Low')}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-text-secondary mb-2">
                    {currentLanguage === 'ar' ? 'المسار' : 'Route'}
                  </h4>
                  <div className="p-4 bg-background-secondary rounded-lg">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="text-center">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mb-2">
                          <Icon name="Building2" size={16} color="var(--color-primary)" />
                        </div>
                        <div className="text-xs text-text-muted">{currentLanguage === 'ar' ? 'من' : 'From'}</div>
                        <div className="text-sm font-medium text-text-primary">{transfer.originWarehouse}</div>
                      </div>
                      <div className="flex-1 flex items-center justify-center">
                        <Icon name="ArrowRight" size={20} className="text-text-muted rtl:rotate-180" />
                      </div>
                      <div className="text-center">
                        <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center mb-2">
                          <Icon name="Building2" size={16} color="var(--color-success)" />
                        </div>
                        <div className="text-xs text-text-muted">{currentLanguage === 'ar' ? 'إلى' : 'To'}</div>
                        <div className="text-sm font-medium text-text-primary">{transfer.destinationWarehouse}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-text-primary mb-4">
                  {currentLanguage === 'ar' ? 'ملخص القيمة' : 'Value Summary'}
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'إجمالي المنتجات:' : 'Total Products:'}</span>
                    <span className="text-sm font-medium text-text-primary">{transfer.products.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-text-muted">{currentLanguage === 'ar' ? 'إجمالي الوحدات:' : 'Total Units:'}</span>
                    <span className="text-sm font-medium text-text-primary">{transfer.totalQuantity}</span>
                  </div>
                  <div className="flex justify-between border-t border-border pt-3">
                    <span className="text-base font-medium text-text-primary">{currentLanguage === 'ar' ? 'إجمالي القيمة:' : 'Total Value:'}</span>
                    <span className="text-base font-semibold text-text-primary">
                      {transfer.totalValue.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Tracking */}
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-4">
                {currentLanguage === 'ar' ? 'تتبع التقدم' : 'Progress Tracking'}
              </h3>
              <div className="flex items-center justify-between mb-6">
                {progressSteps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className="text-center">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                        transfer.progress >= index + 1 ? 'bg-primary text-white' : 'bg-secondary-200 text-text-muted'
                      }`}>
                        <Icon name={step.icon} size={16} />
                      </div>
                      <div className={`text-xs ${
                        transfer.progress >= index + 1 ? 'text-primary font-medium' : 'text-text-muted'
                      }`}>
                        {step.label}
                      </div>
                    </div>
                    {index < progressSteps.length - 1 && (
                      <div className={`w-16 h-0.5 mx-2 ${
                        transfer.progress > index + 1 ? 'bg-primary' : 'bg-secondary-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Tracking Information */}
            {transfer.status === 'in-transit' && transfer.tracking && (
              <div>
                <h3 className="text-lg font-medium text-text-primary mb-4">
                  {currentLanguage === 'ar' ? 'معلومات التتبع' : 'Tracking Information'}
                </h3>
                <div className="p-4 bg-background-secondary rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Icon name="MapPin" size={16} color="var(--color-primary)" />
                      <span className="text-sm font-medium text-text-primary">
                        {currentLanguage === 'ar' ? 'الموقع الحالي:' : 'Current Location:'}
                      </span>
                    </div>
                    <span className="text-sm text-text-primary">{transfer.tracking.currentLocation}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Icon name="Clock" size={16} color="var(--color-success)" />
                      <span className="text-sm font-medium text-text-primary">
                        {currentLanguage === 'ar' ? 'الوصول المتوقع:' : 'Estimated Arrival:'}
                      </span>
                    </div>
                    <span className="text-sm text-success font-medium">{transfer.tracking.estimatedArrival}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Product Details */}
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-4">
                {currentLanguage === 'ar' ? 'تفاصيل المنتجات' : 'Product Details'}
              </h3>
              <div className="space-y-3">
                {transfer.products.map((product) => (
                  <div key={product.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                        <Icon name="Package" size={20} color="var(--color-primary)" />
                      </div>
                      <div>
                        <div className="font-medium text-text-primary">{product.name}</div>
                        <div className="text-sm text-text-muted">
                          {currentLanguage === 'ar' ? 'الكمية:' : 'Quantity:'} {product.quantity} {currentLanguage === 'ar' ? 'وحدة' : 'units'}
                        </div>
                      </div>
                    </div>
                    <div className="text-right rtl:text-left">
                      <div className="font-medium text-text-primary">
                        {product.value.toLocaleString()} {currentLanguage === 'ar' ? 'ريال' : 'SAR'}
                      </div>
                      <div className="text-sm text-text-muted">
                        {(product.value / product.quantity).toLocaleString()} {currentLanguage === 'ar' ? 'ريال/وحدة' : 'SAR/unit'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Approval History */}
            <div>
              <h3 className="text-lg font-medium text-text-primary mb-4">
                {currentLanguage === 'ar' ? 'تاريخ الموافقات' : 'Approval History'}
              </h3>
              <div className="space-y-3">
                {transfer.approvalHistory.map((approval, index) => (
                  <div key={index} className="flex items-start space-x-3 rtl:space-x-reverse p-3 bg-background-secondary rounded-lg">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                      approval.status === 'approved' ? 'bg-success-100' :
                      approval.status === 'rejected' ? 'bg-error-100' : 'bg-warning-100'
                    }`}>
                      <Icon 
                        name={
                          approval.status === 'approved' ? 'Check' :
                          approval.status === 'rejected' ? 'X' : 'Clock'
                        } 
                        size={14} 
                        color={
                          approval.status === 'approved' ? 'var(--color-success)' :
                          approval.status === 'rejected' ? 'var(--color-error)' : 'var(--color-warning)'
                        }
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-text-primary">{approval.approver}</span>
                        <span className="text-xs text-text-muted">{approval.timestamp}</span>
                      </div>
                      <div className="text-sm text-text-muted mt-1">{approval.action}</div>
                      {approval.notes && (
                        <div className="text-sm text-text-secondary mt-2 p-2 bg-surface rounded border-l-2 border-primary">
                          {approval.notes}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Documents */}
            {transfer.documents && transfer.documents.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-text-primary mb-4">
                  {currentLanguage === 'ar' ? 'المستندات المرفقة' : 'Attached Documents'}
                </h3>
                <div className="space-y-2">
                  {transfer.documents.map((doc, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Icon name="FileText" size={16} color="var(--color-primary)" />
                        <span className="text-sm text-text-primary">{doc.name}</span>
                      </div>
                      <Button variant="outline" iconName="Download" className="p-2">
                        <span className="sr-only">{currentLanguage === 'ar' ? 'تحميل' : 'Download'}</span>
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-border">
          <div className="text-sm text-text-muted">
            {currentLanguage === 'ar' ? 'آخر تحديث:' : 'Last updated:'} {transfer.lastUpdated}
          </div>
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            {transfer.status === 'pending' && (
              <>
                <Button variant="outline" iconName="X">
                  {currentLanguage === 'ar' ? 'رفض' : 'Reject'}
                </Button>
                <Button variant="primary" iconName="Check">
                  {currentLanguage === 'ar' ? 'موافقة' : 'Approve'}
                </Button>
              </>
            )}
            <Button variant="outline" onClick={onClose}>
              {currentLanguage === 'ar' ? 'إغلاق' : 'Close'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransferDetailsModal;