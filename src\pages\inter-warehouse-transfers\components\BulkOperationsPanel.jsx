import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const BulkOperationsPanel = ({ selectedTransfers, onBulkAction, onClearSelection, currentLanguage }) => {
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);

  const handleBulkAction = (action) => {
    setPendingAction(action);
    setShowConfirmModal(true);
  };

  const confirmBulkAction = () => {
    onBulkAction(pendingAction, selectedTransfers);
    setShowConfirmModal(false);
    setPendingAction(null);
    onClearSelection();
  };

  const getActionText = (action) => {
    const actionMap = {
      'approve': currentLanguage === 'ar' ? 'الموافقة على' : 'Approve',
      'reject': currentLanguage === 'ar' ? 'رفض' : 'Reject',
      'cancel': currentLanguage === 'ar' ? 'إلغاء' : 'Cancel',
      'export': currentLanguage === 'ar' ? 'تصدير' : 'Export'
    };
    return actionMap[action] || action;
  };

  if (selectedTransfers.length === 0) return null;

  return (
    <>
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
        <div className="bg-surface border border-border rounded-lg shadow-elevation-3 p-4">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Icon name="CheckSquare" size={16} color="var(--color-primary)" />
              <span className="text-sm font-medium text-text-primary">
                {selectedTransfers.length} {currentLanguage === 'ar' ? 'تحويل محدد' : 'transfers selected'}
              </span>
            </div>

            <div className="h-6 w-px bg-border" />

            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Button
                variant="primary"
                onClick={() => handleBulkAction('approve')}
                iconName="Check"
                className="text-sm px-3 py-1.5"
              >
                {currentLanguage === 'ar' ? 'موافقة' : 'Approve'}
              </Button>

              <Button
                variant="outline"
                onClick={() => handleBulkAction('reject')}
                iconName="X"
                className="text-sm px-3 py-1.5"
              >
                {currentLanguage === 'ar' ? 'رفض' : 'Reject'}
              </Button>

              <Button
                variant="outline"
                onClick={() => handleBulkAction('export')}
                iconName="Download"
                className="text-sm px-3 py-1.5"
              >
                {currentLanguage === 'ar' ? 'تصدير' : 'Export'}
              </Button>

              <Button
                variant="ghost"
                onClick={onClearSelection}
                iconName="X"
                className="text-sm px-2 py-1.5"
              >
                {currentLanguage === 'ar' ? 'إلغاء التحديد' : 'Clear'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-surface rounded-lg shadow-elevation-3 w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
                <div className="w-10 h-10 bg-warning-100 rounded-full flex items-center justify-center">
                  <Icon name="AlertTriangle" size={20} color="var(--color-warning)" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-text-primary">
                    {currentLanguage === 'ar' ? 'تأكيد العملية المجمعة' : 'Confirm Bulk Operation'}
                  </h3>
                  <p className="text-sm text-text-muted">
                    {currentLanguage === 'ar' ? 'هذا الإجراء لا يمكن التراجع عنه' : 'This action cannot be undone'}
                  </p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm text-text-secondary">
                  {currentLanguage === 'ar' 
                    ? `هل أنت متأكد من أنك تريد ${getActionText(pendingAction)} ${selectedTransfers.length} تحويل؟`
                    : `Are you sure you want to ${getActionText(pendingAction).toLowerCase()} ${selectedTransfers.length} transfers?`
                  }
                </p>
              </div>

              <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse">
                <Button
                  variant="outline"
                  onClick={() => setShowConfirmModal(false)}
                >
                  {currentLanguage === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button
                  variant={pendingAction === 'reject' || pendingAction === 'cancel' ? 'danger' : 'primary'}
                  onClick={confirmBulkAction}
                  iconName={
                    pendingAction === 'approve' ? 'Check' :
                    pendingAction === 'reject' ? 'X' :
                    pendingAction === 'export' ? 'Download' : 'AlertTriangle'
                  }
                >
                  {getActionText(pendingAction)}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BulkOperationsPanel;