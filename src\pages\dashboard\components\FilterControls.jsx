import React, { useState, useEffect } from 'react';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Icon from '../../../components/AppIcon';

const FilterControls = ({ onFiltersChange }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [filters, setFilters] = useState({
    governorate: 'all',
    dateRange: '7days',
    warehouseType: 'all',
    status: 'all'
  });
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const governorates = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع المحافظات' : 'All Governorates' },
    { value: 'riyadh', label: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh' },
    { value: 'eastern', label: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province' },
    { value: 'makkah', label: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah' },
    { value: 'tabuk', label: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk' },
    { value: 'asir', label: currentLanguage === 'ar' ? 'عسير' : 'Asir' }
  ];

  const dateRanges = [
    { value: '1day', label: currentLanguage === 'ar' ? 'اليوم' : 'Today' },
    { value: '7days', label: currentLanguage === 'ar' ? '7 أيام' : '7 Days' },
    { value: '30days', label: currentLanguage === 'ar' ? '30 يوم' : '30 Days' },
    { value: '90days', label: currentLanguage === 'ar' ? '90 يوم' : '90 Days' },
    { value: 'custom', label: currentLanguage === 'ar' ? 'مخصص' : 'Custom' }
  ];

  const warehouseTypes = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع الأنواع' : 'All Types' },
    { value: 'main', label: currentLanguage === 'ar' ? 'مستودع رئيسي' : 'Main Warehouse' },
    { value: 'regional', label: currentLanguage === 'ar' ? 'مستودع إقليمي' : 'Regional Warehouse' }
  ];

  const statuses = [
    { value: 'all', label: currentLanguage === 'ar' ? 'جميع الحالات' : 'All Statuses' },
    { value: 'active', label: currentLanguage === 'ar' ? 'نشط' : 'Active' },
    { value: 'maintenance', label: currentLanguage === 'ar' ? 'صيانة' : 'Maintenance' },
    { value: 'inactive', label: currentLanguage === 'ar' ? 'غير نشط' : 'Inactive' }
  ];

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const resetFilters = () => {
    const defaultFilters = {
      governorate: 'all',
      dateRange: '7days',
      warehouseType: 'all',
      status: 'all'
    };
    setFilters(defaultFilters);
    onFiltersChange?.(defaultFilters);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== 'all' && value !== '7days');

  return (
    <div className="bg-surface border border-border rounded-lg shadow-elevation-1">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Icon name="Filter" size={20} className="text-text-secondary" />
            <h3 className="text-sm font-medium text-text-primary">
              {currentLanguage === 'ar' ? 'تصفية البيانات' : 'Filter Data'}
            </h3>
            {hasActiveFilters && (
              <span className="px-2 py-1 bg-primary-100 text-primary text-xs rounded-full">
                {currentLanguage === 'ar' ? 'مطبق' : 'Active'}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
                iconName="RotateCcw"
                iconPosition="left"
              >
                {currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset'}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              iconName={isExpanded ? "ChevronUp" : "ChevronDown"}
              iconPosition="right"
            >
              {isExpanded ? (currentLanguage === 'ar' ? 'إخفاء' : 'Hide') : (currentLanguage === 'ar' ? 'إظهار' : 'Show')}
            </Button>
          </div>
        </div>

        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-border">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Governorate Filter */}
              <div>
                <label className="block text-xs font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'المحافظة' : 'Governorate'}
                </label>
                <select
                  value={filters.governorate}
                  onChange={(e) => handleFilterChange('governorate', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface"
                >
                  {governorates.map((gov) => (
                    <option key={gov.value} value={gov.value}>
                      {gov.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Date Range Filter */}
              <div>
                <label className="block text-xs font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'النطاق الزمني' : 'Date Range'}
                </label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface"
                >
                  {dateRanges.map((range) => (
                    <option key={range.value} value={range.value}>
                      {range.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Warehouse Type Filter */}
              <div>
                <label className="block text-xs font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'نوع المستودع' : 'Warehouse Type'}
                </label>
                <select
                  value={filters.warehouseType}
                  onChange={(e) => handleFilterChange('warehouseType', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface"
                >
                  {warehouseTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-xs font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'الحالة' : 'Status'}
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface"
                >
                  {statuses.map((status) => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Custom Date Range */}
            {filters.dateRange === 'custom' && (
              <div className="mt-4 pt-4 border-t border-border">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'من تاريخ' : 'From Date'}
                    </label>
                    <Input
                      type="date"
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'إلى تاريخ' : 'To Date'}
                    </label>
                    <Input
                      type="date"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterControls;