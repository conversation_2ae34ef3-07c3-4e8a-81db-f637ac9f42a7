import React from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
// Add your imports here
import Login from "pages/login";
import Dashboard from "pages/dashboard";
import WarehouseManagement from "pages/warehouse-management";
import AnalyticsDashboard from "pages/analytics-dashboard";
import ProductMovementTracking from "pages/product-movement-tracking";
import InterWarehouseTransfers from "pages/inter-warehouse-transfers";
import InventoryReports from "pages/inventory-reports";
import NotFound from "pages/NotFound";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your routes here */}
        <Route path="/" element={<Dashboard />} />
        <Route path="/login" element={<Login />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/warehouse-management" element={<WarehouseManagement />} />
        <Route path="/analytics-dashboard" element={<AnalyticsDashboard />} />
        <Route path="/product-movement-tracking" element={<ProductMovementTracking />} />
        <Route path="/inter-warehouse-transfers" element={<InterWarehouseTransfers />} />
        <Route path="/inventory-reports" element={<InventoryReports />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;