import React, { useState, useEffect, useRef } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

const WarehouseSelector = ({ onWarehouseChange, className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const dropdownRef = useRef(null);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const warehouses = [
    {
      id: 'wh-001',
      name: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      governorate: currentLanguage === 'ar' ? 'الرياض' : 'Riyadh',
      capacity: 85,
      status: 'active',
      type: 'main'
    },
    {
      id: 'wh-002',
      name: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
      governorate: currentLanguage === 'ar' ? 'المنطقة الشرقية' : 'Eastern Province',
      capacity: 72,
      status: 'active',
      type: 'regional'
    },
    {
      id: 'wh-003',
      name: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
      governorate: currentLanguage === 'ar' ? 'مكة المكرمة' : 'Makkah',
      capacity: 68,
      status: 'active',
      type: 'regional'
    },
    {
      id: 'wh-004',
      name: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk',
      governorate: currentLanguage === 'ar' ? 'تبوك' : 'Tabuk',
      capacity: 45,
      status: 'maintenance',
      type: 'regional'
    },
    {
      id: 'wh-005',
      name: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
      governorate: currentLanguage === 'ar' ? 'عسير' : 'Asir',
      capacity: 58,
      status: 'active',
      type: 'regional'
    }
  ];

  useEffect(() => {
    if (!selectedWarehouse && warehouses.length > 0) {
      const defaultWarehouse = warehouses[0];
      setSelectedWarehouse(defaultWarehouse);
      onWarehouseChange?.(defaultWarehouse);
    }
  }, [warehouses, selectedWarehouse, onWarehouseChange]);

  const filteredWarehouses = warehouses.filter(warehouse =>
    warehouse.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    warehouse.governorate.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleWarehouseSelect = (warehouse) => {
    setSelectedWarehouse(warehouse);
    setIsOpen(false);
    setSearchTerm('');
    onWarehouseChange?.(warehouse);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'text-success';
      case 'maintenance':
        return 'text-warning';
      case 'inactive':
        return 'text-error';
      default:
        return 'text-text-muted';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return currentLanguage === 'ar' ? 'نشط' : 'Active';
      case 'maintenance':
        return currentLanguage === 'ar' ? 'صيانة' : 'Maintenance';
      case 'inactive':
        return currentLanguage === 'ar' ? 'غير نشط' : 'Inactive';
      default:
        return status;
    }
  };

  const getCapacityColor = (capacity) => {
    if (capacity >= 80) return 'text-error';
    if (capacity >= 60) return 'text-warning';
    return 'text-success';
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between min-w-64 text-left"
      >
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <Icon 
              name={selectedWarehouse?.type === 'main' ? 'Building2' : 'Warehouse'} 
              size={16} 
              color="var(--color-primary)" 
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-text-primary truncate">
              {selectedWarehouse?.name || (currentLanguage === 'ar' ? 'اختر مستودع' : 'Select Warehouse')}
            </div>
            {selectedWarehouse && (
              <div className="text-xs text-text-secondary">
                {selectedWarehouse.governorate} • {selectedWarehouse.capacity}% {currentLanguage === 'ar' ? 'ممتلئ' : 'Full'}
              </div>
            )}
          </div>
        </div>
        <Icon name={isOpen ? "ChevronUp" : "ChevronDown"} size={16} />
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-surface border border-border rounded-lg shadow-elevation-2 z-70 max-h-80 overflow-hidden">
          {/* Search */}
          <div className="p-3 border-b border-border">
            <div className="relative">
              <Icon 
                name="Search" 
                size={16} 
                className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-text-muted" 
              />
              <input
                type="text"
                placeholder={currentLanguage === 'ar' ? 'البحث عن المستودعات...' : 'Search warehouses...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 rtl:pr-10 rtl:pl-3 pr-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Warehouse List */}
          <div className="max-h-64 overflow-y-auto">
            {filteredWarehouses.length === 0 ? (
              <div className="p-4 text-center text-text-muted">
                <Icon name="Search" size={24} className="mx-auto mb-2 opacity-50" />
                <p className="text-sm">
                  {currentLanguage === 'ar' ? 'لم يتم العثور على مستودعات' : 'No warehouses found'}
                </p>
              </div>
            ) : (
              <div className="py-2">
                {filteredWarehouses.map((warehouse) => (
                  <button
                    key={warehouse.id}
                    onClick={() => handleWarehouseSelect(warehouse)}
                    className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-left hover:bg-primary-50 transition-colors duration-150 ${
                      selectedWarehouse?.id === warehouse.id ? 'bg-primary-50' : ''
                    }`}
                  >
                    <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon 
                        name={warehouse.type === 'main' ? 'Building2' : 'Warehouse'} 
                        size={18} 
                        color="var(--color-primary)" 
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-text-primary truncate">
                          {warehouse.name}
                        </h4>
                        <div className={`text-xs font-medium ${getStatusColor(warehouse.status)}`}>
                          {getStatusText(warehouse.status)}
                        </div>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-text-secondary">
                          {warehouse.governorate}
                        </p>
                        <div className="flex items-center space-x-2 rtl:space-x-reverse">
                          <div className={`text-xs font-medium ${getCapacityColor(warehouse.capacity)}`}>
                            {warehouse.capacity}%
                          </div>
                          <div className="w-12 h-1.5 bg-secondary-200 rounded-full overflow-hidden">
                            <div 
                              className={`h-full rounded-full transition-all duration-300 ${
                                warehouse.capacity >= 80 ? 'bg-error' :
                                warehouse.capacity >= 60 ? 'bg-warning' : 'bg-success'
                              }`}
                              style={{ width: `${warehouse.capacity}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    {selectedWarehouse?.id === warehouse.id && (
                      <Icon name="Check" size={16} color="var(--color-primary)" />
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default WarehouseSelector;