import React, { useState, useEffect } from 'react';
import HeaderNavigation from '../../components/ui/HeaderNavigation';
import ContextualSidebar from '../../components/ui/ContextualSidebar';
import BreadcrumbNavigation from '../../components/ui/BreadcrumbNavigation';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import TransferStatusTabs from './components/TransferStatusTabs';
import TransferFilters from './components/TransferFilters';
import TransferTable from './components/TransferTable';
import InitiateTransferModal from './components/InitiateTransferModal';
import BulkOperationsPanel from './components/BulkOperationsPanel';

const InterWarehouseTransfers = () => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [filters, setFilters] = useState({});
  const [selectedTransfers, setSelectedTransfers] = useState([]);
  const [showInitiateModal, setShowInitiateModal] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  // Mock transfer data
  const mockTransfers = [
    {
      id: 'TRF-2024-001',
      originWarehouse: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      destinationWarehouse: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam',
      products: [
        { id: 'p-001', name: currentLanguage === 'ar' ? 'لابتوب ديل XPS 13' : 'Dell XPS 13 Laptop', quantity: 15, value: 52500 },
        { id: 'p-002', name: currentLanguage === 'ar' ? 'آيفون 15 برو' : 'iPhone 15 Pro', quantity: 8, value: 33600 }
      ],
      totalQuantity: 23,
      totalValue: 86100,
      status: 'pending',
      priority: 'high',
      requestedDate: '2024-01-15',
      coordinator: currentLanguage === 'ar' ? 'أحمد محمد' : 'Ahmed Mohammed',
      progress: 1,
      lastUpdated: '2024-01-15 10:30 AM',
      approvalHistory: [
        {
          approver: currentLanguage === 'ar' ? 'مدير المستودع - أحمد محمد' : 'Warehouse Manager - Ahmed Mohammed',
          action: currentLanguage === 'ar' ? 'طلب التحويل' : 'Transfer requested',
          status: 'requested',
          timestamp: '2024-01-15 10:30 AM',
          notes: currentLanguage === 'ar' ? 'طلب تحويل عاجل لتلبية الطلب المتزايد' : 'Urgent transfer request to meet increased demand'
        }
      ]
    },
    {
      id: 'TRF-2024-002',
      originWarehouse: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah',
      destinationWarehouse: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh',
      products: [
        { id: 'p-003', name: currentLanguage === 'ar' ? 'سامسونج جالاكسي S24' : 'Samsung Galaxy S24', quantity: 25, value: 70000 },
        { id: 'p-004', name: currentLanguage === 'ar' ? 'ماك بوك برو 16 إنش' : 'MacBook Pro 16"', quantity: 5, value: 42500 }
      ],
      totalQuantity: 30,
      totalValue: 112500,
      status: 'in-transit',
      priority: 'normal',
      requestedDate: '2024-01-12',
      coordinator: currentLanguage === 'ar' ? 'فاطمة أحمد' : 'Fatima Ahmed',
      progress: 4,
      lastUpdated: '2024-01-14 02:15 PM',
      tracking: {
        currentLocation: currentLanguage === 'ar' ? 'الطائف - نقطة التفتيش الوسطى' : 'Taif - Central Checkpoint',
        estimatedArrival: '2024-01-16 09:00 AM'
      },
      approvalHistory: [
        {
          approver: currentLanguage === 'ar' ? 'مدير المستودع - فاطمة أحمد' : 'Warehouse Manager - Fatima Ahmed',
          action: currentLanguage === 'ar' ? 'طلب التحويل' : 'Transfer requested',
          status: 'requested',
          timestamp: '2024-01-12 09:15 AM'
        },
        {
          approver: currentLanguage === 'ar' ? 'مشرف العمليات - محمد علي' : 'Operations Supervisor - Mohammed Ali',
          action: currentLanguage === 'ar' ? 'موافقة على التحويل' : 'Transfer approved',
          status: 'approved',
          timestamp: '2024-01-12 11:30 AM',
          notes: currentLanguage === 'ar' ? 'موافق عليه للمعالجة الفورية' : 'Approved for immediate processing'
        }
      ]
    },
    {
      id: 'TRF-2024-003',
      originWarehouse: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk',
      destinationWarehouse: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha',
      products: [
        { id: 'p-005', name: currentLanguage === 'ar' ? 'آيباد برو 12.9' : 'iPad Pro 12.9"', quantity: 12, value: 45600 }
      ],
      totalQuantity: 12,
      totalValue: 45600,
      status: 'completed',
      priority: 'low',
      requestedDate: '2024-01-10',
      coordinator: currentLanguage === 'ar' ? 'سارة خالد' : 'Sara Khalid',
      progress: 5,
      lastUpdated: '2024-01-13 04:45 PM',
      approvalHistory: [
        {
          approver: currentLanguage === 'ar' ? 'مدير المستودع - سارة خالد' : 'Warehouse Manager - Sara Khalid',
          action: currentLanguage === 'ar' ? 'طلب التحويل' : 'Transfer requested',
          status: 'requested',
          timestamp: '2024-01-10 08:00 AM'
        },
        {
          approver: currentLanguage === 'ar' ? 'مشرف العمليات - عبدالله سعد' : 'Operations Supervisor - Abdullah Saad',
          action: currentLanguage === 'ar' ? 'موافقة على التحويل' : 'Transfer approved',
          status: 'approved',
          timestamp: '2024-01-10 10:15 AM'
        },
        {
          approver: currentLanguage === 'ar' ? 'منسق اللوجستيات - نورا محمد' : 'Logistics Coordinator - Nora Mohammed',
          action: currentLanguage === 'ar' ? 'تم التسليم بنجاح' : 'Successfully delivered',
          status: 'completed',
          timestamp: '2024-01-13 04:45 PM',
          notes: currentLanguage === 'ar' ? 'تم التسليم في الوقت المحدد بدون مشاكل' : 'Delivered on time without issues'
        }
      ],
      documents: [
        { name: currentLanguage === 'ar' ? 'إيصال التسليم.pdf' : 'Delivery Receipt.pdf', type: 'pdf' },
        { name: currentLanguage === 'ar' ? 'تفويض التحويل.pdf' : 'Transfer Authorization.pdf', type: 'pdf' }
      ]
    }
  ];

  const getFilteredTransfers = () => {
    let filtered = mockTransfers;

    // Filter by active tab
    if (activeTab !== 'all') {
      filtered = filtered.filter(transfer => {
        switch (activeTab) {
          case 'pending':
            return transfer.status === 'pending';
          case 'in-transit':
            return transfer.status === 'in-transit';
          case 'completed':
            return transfer.status === 'completed';
          default:
            return true;
        }
      });
    }

    // Apply additional filters
    if (filters.search) {
      filtered = filtered.filter(transfer =>
        transfer.id.toLowerCase().includes(filters.search.toLowerCase()) ||
        transfer.originWarehouse.toLowerCase().includes(filters.search.toLowerCase()) ||
        transfer.destinationWarehouse.toLowerCase().includes(filters.search.toLowerCase()) ||
        transfer.coordinator.toLowerCase().includes(filters.search.toLowerCase())
      );
    }

    if (filters.priority) {
      filtered = filtered.filter(transfer => transfer.priority === filters.priority);
    }

    if (filters.minValue || filters.maxValue) {
      filtered = filtered.filter(transfer => {
        const value = transfer.totalValue;
        const min = filters.minValue ? parseFloat(filters.minValue) : 0;
        const max = filters.maxValue ? parseFloat(filters.maxValue) : Infinity;
        return value >= min && value <= max;
      });
    }

    return filtered;
  };

  const getTransferCounts = () => {
    return {
      all: mockTransfers.length,
      pending: mockTransfers.filter(t => t.status === 'pending').length,
      inTransit: mockTransfers.filter(t => t.status === 'in-transit').length,
      completed: mockTransfers.filter(t => t.status === 'completed').length
    };
  };

  const handleStatusUpdate = (transferId, newStatus) => {
    console.log(`Updating transfer ${transferId} to status: ${newStatus}`);
    // In a real app, this would update the backend
  };

  const handleInitiateTransfer = (transferData) => {
    console.log('Creating new transfer:', transferData);
    // In a real app, this would create a new transfer
  };

  const handleBulkAction = (action, transfers) => {
    console.log(`Performing bulk ${action} on transfers:`, transfers);
    // In a real app, this would perform the bulk operation
  };

  const filteredTransfers = getFilteredTransfers();
  const transferCounts = getTransferCounts();

  return (
    <div className="min-h-screen bg-background">
      <HeaderNavigation />
      
      <div className="flex">
        <ContextualSidebar 
          isOpen={isSidebarOpen}
          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}
        />
        
        <main className="flex-1 lg:ml-80">
          <div className="p-6">
            {/* Header Section */}
            <div className="mb-6">
              <BreadcrumbNavigation warehouseContext={selectedWarehouse} />
              
              <div className="flex items-center justify-between mt-4">
                <div>
                  <h1 className="text-2xl font-semibold text-text-primary">
                    {currentLanguage === 'ar' ? 'التحويلات بين المستودعات' : 'Inter-Warehouse Transfers'}
                  </h1>
                  <p className="text-text-muted mt-1">
                    {currentLanguage === 'ar' ?'إدارة وتتبع حركة المنتجات بين المستودعات' :'Manage and track product movement between warehouse locations'
                    }
                  </p>
                </div>
                
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Button
                    variant="outline"
                    onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                    className="lg:hidden"
                    iconName="Menu"
                  />
                  
                  <Button
                    variant="primary"
                    onClick={() => setShowInitiateModal(true)}
                    iconName="Plus"
                    iconPosition="left"
                  >
                    {currentLanguage === 'ar' ? 'إنشاء تحويل' : 'Initiate Transfer'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Status Tabs */}
            <TransferStatusTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              transferCounts={transferCounts}
              currentLanguage={currentLanguage}
            />

            {/* Filters */}
            <TransferFilters
              onFiltersChange={setFilters}
              currentLanguage={currentLanguage}
            />

            {/* Results Summary */}
            <div className="flex items-center justify-between py-4 px-6 bg-surface border-b border-border">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <span className="text-sm text-text-muted">
                  {currentLanguage === 'ar' 
                    ? `عرض ${filteredTransfers.length} من ${mockTransfers.length} تحويل`
                    : `Showing ${filteredTransfers.length} of ${mockTransfers.length} transfers`
                  }
                </span>
                
                {selectedTransfers.length > 0 && (
                  <span className="text-sm font-medium text-primary">
                    {selectedTransfers.length} {currentLanguage === 'ar' ? 'محدد' : 'selected'}
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Button variant="outline" iconName="Filter" className="text-sm px-3 py-1.5">
                  {currentLanguage === 'ar' ? 'فلتر' : 'Filter'}
                </Button>
                <Button variant="outline" iconName="Download" className="text-sm px-3 py-1.5">
                  {currentLanguage === 'ar' ? 'تصدير' : 'Export'}
                </Button>
              </div>
            </div>

            {/* Transfer Table */}
            <TransferTable
              transfers={filteredTransfers}
              onStatusUpdate={handleStatusUpdate}
              currentLanguage={currentLanguage}
            />

            {/* Empty State */}
            {filteredTransfers.length === 0 && (
              <div className="text-center py-12 bg-surface border border-border rounded-lg">
                <Icon name="ArrowRightLeft" size={48} className="mx-auto text-text-muted mb-4" />
                <h3 className="text-lg font-medium text-text-primary mb-2">
                  {currentLanguage === 'ar' ? 'لا توجد تحويلات' : 'No transfers found'}
                </h3>
                <p className="text-text-muted mb-6">
                  {currentLanguage === 'ar' ?'لم يتم العثور على تحويلات تطابق المعايير المحددة' :'No transfers match the selected criteria'
                  }
                </p>
                <Button
                  variant="primary"
                  onClick={() => setShowInitiateModal(true)}
                  iconName="Plus"
                  iconPosition="left"
                >
                  {currentLanguage === 'ar' ? 'إنشاء تحويل جديد' : 'Create New Transfer'}
                </Button>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Modals */}
      <InitiateTransferModal
        isOpen={showInitiateModal}
        onClose={() => setShowInitiateModal(false)}
        onSubmit={handleInitiateTransfer}
        currentLanguage={currentLanguage}
      />

      {/* Bulk Operations Panel */}
      <BulkOperationsPanel
        selectedTransfers={selectedTransfers}
        onBulkAction={handleBulkAction}
        onClearSelection={() => setSelectedTransfers([])}
        currentLanguage={currentLanguage}
      />
    </div>
  );
};

export default InterWarehouseTransfers;