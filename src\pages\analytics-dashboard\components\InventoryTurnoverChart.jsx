import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';

const InventoryTurnoverChart = ({ data, currentLanguage }) => {
  const COLORS = [
    'var(--color-success)',
    'var(--color-primary)',
    'var(--color-accent)',
    'var(--color-warning)',
    'var(--color-error)',
    'var(--color-secondary)'
  ];

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-surface border border-border rounded-lg shadow-elevation-2 p-3">
          <p className="font-medium text-text-primary">{data.category}</p>
          <p className="text-sm text-text-secondary">
            {currentLanguage === 'ar' ? 'معدل الدوران:' : 'Turnover Rate:'} {data.turnoverRate}x
          </p>
          <p className="text-sm text-text-secondary">
            {currentLanguage === 'ar' ? 'القيمة:' : 'Value:'} ${data.value.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Don't show labels for slices less than 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="600"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-text-primary">
          {currentLanguage === 'ar' ? 'معدل دوران المخزون' : 'Inventory Turnover Rates'}
        </h3>
        <p className="text-sm text-text-secondary">
          {currentLanguage === 'ar' ? 'حسب فئة المنتج' : 'By product category'}
        </p>
      </div>
      <div className="card-content">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomLabel}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                iconType="circle"
                wrapperStyle={{ paddingTop: '20px', fontSize: '12px' }}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4 mt-6 pt-4 border-t border-border">
          <div className="text-center">
            <p className="text-2xl font-bold text-text-primary">
              {(data.reduce((sum, item) => sum + item.turnoverRate, 0) / data.length).toFixed(1)}x
            </p>
            <p className="text-sm text-text-secondary">
              {currentLanguage === 'ar' ? 'متوسط الدوران' : 'Average Turnover'}
            </p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-text-primary">
              ${data.reduce((sum, item) => sum + item.value, 0).toLocaleString()}
            </p>
            <p className="text-sm text-text-secondary">
              {currentLanguage === 'ar' ? 'إجمالي القيمة' : 'Total Value'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventoryTurnoverChart;