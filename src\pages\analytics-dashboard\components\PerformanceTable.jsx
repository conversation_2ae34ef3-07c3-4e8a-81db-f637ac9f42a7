import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const PerformanceTable = ({ data, currentLanguage }) => {
  const [sortField, setSortField] = useState('efficiency');
  const [sortDirection, setSortDirection] = useState('desc');

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const sortedData = [...data].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 90) return 'text-success';
    if (efficiency >= 75) return 'text-warning';
    return 'text-error';
  };

  const getEfficiencyBadge = (efficiency) => {
    if (efficiency >= 90) return 'bg-success-100 text-success';
    if (efficiency >= 75) return 'bg-warning-100 text-warning';
    return 'bg-error-100 text-error';
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return { icon: 'Trophy', color: 'var(--color-accent)' };
      case 2:
        return { icon: 'Medal', color: 'var(--color-secondary)' };
      case 3:
        return { icon: 'Award', color: 'var(--color-warning)' };
      default:
        return { icon: 'Hash', color: 'var(--color-text-muted)' };
    }
  };

  const SortIcon = ({ field }) => {
    if (sortField !== field) {
      return <Icon name="ArrowUpDown" size={14} className="text-text-muted" />;
    }
    return (
      <Icon 
        name={sortDirection === 'asc' ? 'ArrowUp' : 'ArrowDown'} 
        size={14} 
        className="text-primary" 
      />
    );
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary">
              {currentLanguage === 'ar' ? 'أداء المستودعات' : 'Warehouse Performance'}
            </h3>
            <p className="text-sm text-text-secondary">
              {currentLanguage === 'ar' ? 'ترتيب حسب مقاييس الكفاءة' : 'Ranked by efficiency metrics'}
            </p>
          </div>
          <Button variant="outline" iconName="Download" iconPosition="left">
            {currentLanguage === 'ar' ? 'تصدير' : 'Export'}
          </Button>
        </div>
      </div>
      <div className="card-content p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-background-secondary">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'الترتيب' : 'Rank'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'المستودع' : 'Warehouse'}
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-primary-50 transition-colors"
                  onClick={() => handleSort('efficiency')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'الكفاءة' : 'Efficiency'}</span>
                    <SortIcon field="efficiency" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-primary-50 transition-colors"
                  onClick={() => handleSort('throughput')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'الإنتاجية' : 'Throughput'}</span>
                    <SortIcon field="throughput" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-primary-50 transition-colors"
                  onClick={() => handleSort('costEfficiency')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'كفاءة التكلفة' : 'Cost Efficiency'}</span>
                    <SortIcon field="costEfficiency" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-primary-50 transition-colors"
                  onClick={() => handleSort('accuracy')}
                >
                  <div className="flex items-center space-x-1 rtl:space-x-reverse">
                    <span>{currentLanguage === 'ar' ? 'الدقة' : 'Accuracy'}</span>
                    <SortIcon field="accuracy" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  {currentLanguage === 'ar' ? 'الحالة' : 'Status'}
                </th>
              </tr>
            </thead>
            <tbody className="bg-surface divide-y divide-border">
              {sortedData.map((warehouse, index) => {
                const rankInfo = getRankIcon(index + 1);
                return (
                  <tr key={warehouse.id} className="hover:bg-primary-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Icon name={rankInfo.icon} size={16} color={rankInfo.color} />
                        <span className="text-sm font-medium text-text-primary">
                          #{index + 1}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Icon name="Building2" size={16} color="var(--color-primary)" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-text-primary">
                            {warehouse.name}
                          </p>
                          <p className="text-xs text-text-secondary">
                            {warehouse.location}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <span className={`text-sm font-medium ${getEfficiencyColor(warehouse.efficiency)}`}>
                          {warehouse.efficiency}%
                        </span>
                        <div className="w-16 h-2 bg-secondary-200 rounded-full overflow-hidden">
                          <div 
                            className={`h-full rounded-full transition-all duration-300 ${
                              warehouse.efficiency >= 90 ? 'bg-success' :
                              warehouse.efficiency >= 75 ? 'bg-warning' : 'bg-error'
                            }`}
                            style={{ width: `${warehouse.efficiency}%` }}
                          />
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-text-primary">
                        {warehouse.throughput.toLocaleString()}
                      </span>
                      <span className="text-xs text-text-secondary ml-1">
                        {currentLanguage === 'ar' ? 'وحدة/يوم' : 'units/day'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-text-primary">
                        ${warehouse.costEfficiency.toFixed(2)}
                      </span>
                      <span className="text-xs text-text-secondary ml-1">
                        {currentLanguage === 'ar' ? '/وحدة' : '/unit'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-text-primary">
                        {warehouse.accuracy}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEfficiencyBadge(warehouse.efficiency)}`}>
                        {warehouse.efficiency >= 90 
                          ? (currentLanguage === 'ar' ? 'ممتاز' : 'Excellent')
                          : warehouse.efficiency >= 75 
                          ? (currentLanguage === 'ar' ? 'جيد' : 'Good')
                          : (currentLanguage === 'ar' ? 'يحتاج تحسين' : 'Needs Improvement')
                        }
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PerformanceTable;