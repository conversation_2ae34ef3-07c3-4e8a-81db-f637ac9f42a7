import React, { useState, useEffect } from 'react';

import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const MovementModal = ({ isOpen, onClose, movementType, onSubmit, className = "" }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [formData, setFormData] = useState({
    productName: '',
    productSku: '',
    quantity: '',
    unitPrice: '',
    warehouse: '',
    location: '',
    supplier: '',
    destination: '',
    originWarehouse: '',
    destinationWarehouse: '',
    notes: '',
    attachments: []
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  useEffect(() => {
    if (!isOpen) {
      setFormData({
        productName: '',
        productSku: '',
        quantity: '',
        unitPrice: '',
        warehouse: '',
        location: '',
        supplier: '',
        destination: '',
        originWarehouse: '',
        destinationWarehouse: '',
        notes: '',
        attachments: []
      });
      setErrors({});
    }
  }, [isOpen]);

  const warehouses = [
    { value: 'wh-001', label: currentLanguage === 'ar' ? 'المستودع المركزي - الرياض' : 'Central Warehouse - Riyadh' },
    { value: 'wh-002', label: currentLanguage === 'ar' ? 'مستودع الشرق - الدمام' : 'Eastern Warehouse - Dammam' },
    { value: 'wh-003', label: currentLanguage === 'ar' ? 'مستودع الغرب - جدة' : 'Western Warehouse - Jeddah' },
    { value: 'wh-004', label: currentLanguage === 'ar' ? 'مستودع الشمال - تبوك' : 'Northern Warehouse - Tabuk' },
    { value: 'wh-005', label: currentLanguage === 'ar' ? 'مستودع الجنوب - أبها' : 'Southern Warehouse - Abha' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const calculateTotalValue = () => {
    const quantity = parseFloat(formData.quantity) || 0;
    const unitPrice = parseFloat(formData.unitPrice) || 0;
    return quantity * unitPrice;
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.productName.trim()) {
      newErrors.productName = currentLanguage === 'ar' ? 'اسم المنتج مطلوب' : 'Product name is required';
    }
    if (!formData.productSku.trim()) {
      newErrors.productSku = currentLanguage === 'ar' ? 'رمز المنتج مطلوب' : 'Product SKU is required';
    }
    if (!formData.quantity || parseFloat(formData.quantity) <= 0) {
      newErrors.quantity = currentLanguage === 'ar' ? 'الكمية يجب أن تكون أكبر من صفر' : 'Quantity must be greater than zero';
    }
    if (!formData.unitPrice || parseFloat(formData.unitPrice) <= 0) {
      newErrors.unitPrice = currentLanguage === 'ar' ? 'سعر الوحدة يجب أن يكون أكبر من صفر' : 'Unit price must be greater than zero';
    }

    // Movement type specific validations
    if (movementType === 'entry') {
      if (!formData.warehouse) {
        newErrors.warehouse = currentLanguage === 'ar' ? 'المستودع مطلوب' : 'Warehouse is required';
      }
      if (!formData.supplier.trim()) {
        newErrors.supplier = currentLanguage === 'ar' ? 'المورد مطلوب' : 'Supplier is required';
      }
    } else if (movementType === 'exit') {
      if (!formData.warehouse) {
        newErrors.warehouse = currentLanguage === 'ar' ? 'المستودع مطلوب' : 'Warehouse is required';
      }
      if (!formData.destination.trim()) {
        newErrors.destination = currentLanguage === 'ar' ? 'الوجهة مطلوبة' : 'Destination is required';
      }
    } else if (movementType === 'transfer') {
      if (!formData.originWarehouse) {
        newErrors.originWarehouse = currentLanguage === 'ar' ? 'مستودع المصدر مطلوب' : 'Origin warehouse is required';
      }
      if (!formData.destinationWarehouse) {
        newErrors.destinationWarehouse = currentLanguage === 'ar' ? 'مستودع الوجهة مطلوب' : 'Destination warehouse is required';
      }
      if (formData.originWarehouse === formData.destinationWarehouse) {
        newErrors.destinationWarehouse = currentLanguage === 'ar' ? 'مستودع الوجهة يجب أن يكون مختلف عن مستودع المصدر' : 'Destination warehouse must be different from origin warehouse';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const movementData = {
        ...formData,
        movementType,
        totalValue: calculateTotalValue(),
        timestamp: new Date().toISOString()
      };
      
      await onSubmit(movementData);
      onClose();
    } catch (error) {
      console.error('Error submitting movement:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    switch (movementType) {
      case 'entry':
        return currentLanguage === 'ar' ? 'إضافة دخول منتج' : 'Add Product Entry';
      case 'exit':
        return currentLanguage === 'ar' ? 'إضافة خروج منتج' : 'Add Product Exit';
      case 'transfer':
        return currentLanguage === 'ar' ? 'إضافة تحويل منتج' : 'Add Product Transfer';
      default:
        return currentLanguage === 'ar' ? 'إضافة حركة منتج' : 'Add Product Movement';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className={`bg-surface rounded-lg shadow-elevation-3 w-full max-w-2xl max-h-[90vh] overflow-hidden ${className}`}>
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-text-primary">{getModalTitle()}</h2>
          <Button
            variant="ghost"
            onClick={onClose}
            className="p-2"
            iconName="X"
          />
        </div>

        {/* Modal Content */}
        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto p-6 space-y-6">
            {/* Product Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-text-primary">
                {currentLanguage === 'ar' ? 'معلومات المنتج' : 'Product Information'}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">
                    {currentLanguage === 'ar' ? 'اسم المنتج *' : 'Product Name *'}
                  </label>
                  <Input
                    type="text"
                    value={formData.productName}
                    onChange={(e) => handleInputChange('productName', e.target.value)}
                    placeholder={currentLanguage === 'ar' ? 'أدخل اسم المنتج' : 'Enter product name'}
                    className={errors.productName ? 'border-error' : ''}
                  />
                  {errors.productName && (
                    <p className="text-error text-xs mt-1">{errors.productName}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">
                    {currentLanguage === 'ar' ? 'رمز المنتج *' : 'Product SKU *'}
                  </label>
                  <Input
                    type="text"
                    value={formData.productSku}
                    onChange={(e) => handleInputChange('productSku', e.target.value)}
                    placeholder={currentLanguage === 'ar' ? 'أدخل رمز المنتج' : 'Enter product SKU'}
                    className={errors.productSku ? 'border-error' : ''}
                  />
                  {errors.productSku && (
                    <p className="text-error text-xs mt-1">{errors.productSku}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">
                    {currentLanguage === 'ar' ? 'الكمية *' : 'Quantity *'}
                  </label>
                  <Input
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => handleInputChange('quantity', e.target.value)}
                    placeholder="0"
                    min="1"
                    className={errors.quantity ? 'border-error' : ''}
                  />
                  {errors.quantity && (
                    <p className="text-error text-xs mt-1">{errors.quantity}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">
                    {currentLanguage === 'ar' ? 'سعر الوحدة (ر.س) *' : 'Unit Price (SAR) *'}
                  </label>
                  <Input
                    type="number"
                    value={formData.unitPrice}
                    onChange={(e) => handleInputChange('unitPrice', e.target.value)}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    className={errors.unitPrice ? 'border-error' : ''}
                  />
                  {errors.unitPrice && (
                    <p className="text-error text-xs mt-1">{errors.unitPrice}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">
                    {currentLanguage === 'ar' ? 'القيمة الإجمالية' : 'Total Value'}
                  </label>
                  <div className="px-3 py-2 bg-secondary-50 border border-border rounded-md text-sm font-semibold text-text-primary">
                    {formatCurrency(calculateTotalValue())}
                  </div>
                </div>
              </div>
            </div>

            {/* Movement Specific Fields */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-text-primary">
                {currentLanguage === 'ar' ? 'تفاصيل الحركة' : 'Movement Details'}
              </h3>

              {movementType === 'entry' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'المستودع *' : 'Warehouse *'}
                    </label>
                    <select
                      value={formData.warehouse}
                      onChange={(e) => handleInputChange('warehouse', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary ${errors.warehouse ? 'border-error' : 'border-border'}`}
                    >
                      <option value="">
                        {currentLanguage === 'ar' ? 'اختر المستودع' : 'Select Warehouse'}
                      </option>
                      {warehouses.map((warehouse) => (
                        <option key={warehouse.value} value={warehouse.value}>
                          {warehouse.label}
                        </option>
                      ))}
                    </select>
                    {errors.warehouse && (
                      <p className="text-error text-xs mt-1">{errors.warehouse}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'المورد *' : 'Supplier *'}
                    </label>
                    <Input
                      type="text"
                      value={formData.supplier}
                      onChange={(e) => handleInputChange('supplier', e.target.value)}
                      placeholder={currentLanguage === 'ar' ? 'أدخل اسم المورد' : 'Enter supplier name'}
                      className={errors.supplier ? 'border-error' : ''}
                    />
                    {errors.supplier && (
                      <p className="text-error text-xs mt-1">{errors.supplier}</p>
                    )}
                  </div>
                </div>
              )}

              {movementType === 'exit' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'المستودع *' : 'Warehouse *'}
                    </label>
                    <select
                      value={formData.warehouse}
                      onChange={(e) => handleInputChange('warehouse', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary ${errors.warehouse ? 'border-error' : 'border-border'}`}
                    >
                      <option value="">
                        {currentLanguage === 'ar' ? 'اختر المستودع' : 'Select Warehouse'}
                      </option>
                      {warehouses.map((warehouse) => (
                        <option key={warehouse.value} value={warehouse.value}>
                          {warehouse.label}
                        </option>
                      ))}
                    </select>
                    {errors.warehouse && (
                      <p className="text-error text-xs mt-1">{errors.warehouse}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'الوجهة *' : 'Destination *'}
                    </label>
                    <Input
                      type="text"
                      value={formData.destination}
                      onChange={(e) => handleInputChange('destination', e.target.value)}
                      placeholder={currentLanguage === 'ar' ? 'أدخل الوجهة' : 'Enter destination'}
                      className={errors.destination ? 'border-error' : ''}
                    />
                    {errors.destination && (
                      <p className="text-error text-xs mt-1">{errors.destination}</p>
                    )}
                  </div>
                </div>
              )}

              {movementType === 'transfer' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'مستودع المصدر *' : 'Origin Warehouse *'}
                    </label>
                    <select
                      value={formData.originWarehouse}
                      onChange={(e) => handleInputChange('originWarehouse', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary ${errors.originWarehouse ? 'border-error' : 'border-border'}`}
                    >
                      <option value="">
                        {currentLanguage === 'ar' ? 'اختر مستودع المصدر' : 'Select Origin Warehouse'}
                      </option>
                      {warehouses.map((warehouse) => (
                        <option key={warehouse.value} value={warehouse.value}>
                          {warehouse.label}
                        </option>
                      ))}
                    </select>
                    {errors.originWarehouse && (
                      <p className="text-error text-xs mt-1">{errors.originWarehouse}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-2">
                      {currentLanguage === 'ar' ? 'مستودع الوجهة *' : 'Destination Warehouse *'}
                    </label>
                    <select
                      value={formData.destinationWarehouse}
                      onChange={(e) => handleInputChange('destinationWarehouse', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary ${errors.destinationWarehouse ? 'border-error' : 'border-border'}`}
                    >
                      <option value="">
                        {currentLanguage === 'ar' ? 'اختر مستودع الوجهة' : 'Select Destination Warehouse'}
                      </option>
                      {warehouses.map((warehouse) => (
                        <option key={warehouse.value} value={warehouse.value}>
                          {warehouse.label}
                        </option>
                      ))}
                    </select>
                    {errors.destinationWarehouse && (
                      <p className="text-error text-xs mt-1">{errors.destinationWarehouse}</p>
                    )}
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'الموقع' : 'Location'}
                </label>
                <Input
                  type="text"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder={currentLanguage === 'ar' ? 'أدخل الموقع (اختياري)' : 'Enter location (optional)'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  {currentLanguage === 'ar' ? 'الملاحظات' : 'Notes'}
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder={currentLanguage === 'ar' ? 'أدخل أي ملاحظات إضافية...' : 'Enter any additional notes...'}
                  rows={3}
                  className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-surface text-text-primary resize-none"
                />
              </div>
            </div>
          </div>

          {/* Modal Footer */}
          <div className="flex items-center justify-end space-x-3 rtl:space-x-reverse p-6 border-t border-border">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              {currentLanguage === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {currentLanguage === 'ar' ? 'حفظ الحركة' : 'Save Movement'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MovementModal;