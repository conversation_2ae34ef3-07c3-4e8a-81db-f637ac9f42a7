import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const StockMovementTable = ({ data, currentLanguage }) => {
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Mock expanded data for demonstration
  const expandedData = [
    { date: '2024-01-01', inbound: 245, outbound: 189, transfers: 34, netMovement: 90 },
    { date: '2024-01-02', inbound: 267, outbound: 203, transfers: 28, netMovement: 92 },
    { date: '2024-01-03', inbound: 198, outbound: 234, transfers: 41, netMovement: 5 },
    { date: '2024-01-04', inbound: 289, outbound: 178, transfers: 37, netMovement: 148 },
    { date: '2024-01-05', inbound: 234, outbound: 256, transfers: 29, netMovement: 7 },
    { date: '2024-01-06', inbound: 312, outbound: 198, transfers: 45, netMovement: 159 },
    { date: '2024-01-07', inbound: 278, outbound: 223, transfers: 38, netMovement: 93 },
    { date: '2024-01-08', inbound: 195, outbound: 245, transfers: 32, netMovement: -18 },
    { date: '2024-01-09', inbound: 301, outbound: 187, transfers: 41, netMovement: 155 },
    { date: '2024-01-10', inbound: 256, outbound: 234, transfers: 28, netMovement: 50 }
  ];

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedData = [...expandedData].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const paginatedData = sortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return currentLanguage === 'ar' ? date.toLocaleDateString('ar-SA')
      : date.toLocaleDateString('en-US');
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return 'ArrowUpDown';
    return sortDirection === 'asc' ? 'ArrowUp' : 'ArrowDown';
  };

  const getMovementColor = (value) => {
    if (value > 0) return 'text-success';
    if (value < 0) return 'text-error';
    return 'text-text-secondary';
  };

  const getMovementIcon = (value) => {
    if (value > 0) return 'TrendingUp';
    if (value < 0) return 'TrendingDown';
    return 'Minus';
  };

  return (
    <div className="bg-surface border border-border rounded-lg">
      {/* Table Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-text-primary">
              {currentLanguage === 'ar' ? 'تفاصيل حركة المخزون' : 'Stock Movement Details'}
            </h3>
            <p className="text-sm text-text-secondary mt-1">
              {currentLanguage === 'ar' ? 'تتبع حركة المنتجات اليومية والتحويلات' : 'Track daily product movements and transfers'}
            </p>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Button variant="outline" size="sm" iconName="Filter">
              {currentLanguage === 'ar' ? 'فلتر' : 'Filter'}
            </Button>
            <Button variant="outline" size="sm" iconName="Download">
              {currentLanguage === 'ar' ? 'تصدير' : 'Export'}
            </Button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-background border-b border-border">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                <button
                  onClick={() => handleSort('date')}
                  className="flex items-center space-x-2 rtl:space-x-reverse hover:text-text-primary transition-colors"
                >
                  <span>{currentLanguage === 'ar' ? 'التاريخ' : 'Date'}</span>
                  <Icon name={getSortIcon('date')} size={14} />
                </button>
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                <button
                  onClick={() => handleSort('inbound')}
                  className="flex items-center space-x-2 rtl:space-x-reverse hover:text-text-primary transition-colors"
                >
                  <span>{currentLanguage === 'ar' ? 'الداخل' : 'Inbound'}</span>
                  <Icon name={getSortIcon('inbound')} size={14} />
                </button>
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                <button
                  onClick={() => handleSort('outbound')}
                  className="flex items-center space-x-2 rtl:space-x-reverse hover:text-text-primary transition-colors"
                >
                  <span>{currentLanguage === 'ar' ? 'الخارج' : 'Outbound'}</span>
                  <Icon name={getSortIcon('outbound')} size={14} />
                </button>
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                <button
                  onClick={() => handleSort('transfers')}
                  className="flex items-center space-x-2 rtl:space-x-reverse hover:text-text-primary transition-colors"
                >
                  <span>{currentLanguage === 'ar' ? 'التحويلات' : 'Transfers'}</span>
                  <Icon name={getSortIcon('transfers')} size={14} />
                </button>
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                <button
                  onClick={() => handleSort('netMovement')}
                  className="flex items-center space-x-2 rtl:space-x-reverse hover:text-text-primary transition-colors"
                >
                  <span>{currentLanguage === 'ar' ? 'صافي الحركة' : 'Net Movement'}</span>
                  <Icon name={getSortIcon('netMovement')} size={14} />
                </button>
              </th>
            </tr>
          </thead>
          <tbody className="bg-surface divide-y divide-border">
            {paginatedData.map((row, index) => (
              <tr key={index} className="hover:bg-surface-hover transition-colors">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-primary">
                  {formatDate(row.date)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Icon name="ArrowDown" size={16} className="text-success" />
                    <span className="text-sm font-medium text-text-primary">
                      {row.inbound.toLocaleString()}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Icon name="ArrowUp" size={16} className="text-error" />
                    <span className="text-sm font-medium text-text-primary">
                      {row.outbound.toLocaleString()}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Icon name="ArrowRightLeft" size={16} className="text-primary" />
                    <span className="text-sm font-medium text-text-primary">
                      {row.transfers.toLocaleString()}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={`flex items-center space-x-2 rtl:space-x-reverse ${getMovementColor(row.netMovement)}`}>
                    <Icon name={getMovementIcon(row.netMovement)} size={16} />
                    <span className="text-sm font-medium">
                      {row.netMovement > 0 ? '+' : ''}{row.netMovement.toLocaleString()}
                    </span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-border">
          <div className="flex items-center justify-between">
            <div className="text-sm text-text-secondary">
              {currentLanguage === 'ar' 
                ? `عرض ${(currentPage - 1) * itemsPerPage + 1} إلى ${Math.min(currentPage * itemsPerPage, sortedData.length)} من ${sortedData.length} نتيجة`
                : `Showing ${(currentPage - 1) * itemsPerPage + 1} to ${Math.min(currentPage * itemsPerPage, sortedData.length)} of ${sortedData.length} results`
              }
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Button
                variant="outline"
                size="sm"
                iconName="ChevronLeft"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                {currentLanguage === 'ar' ? 'السابق' : 'Previous'}
              </Button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={currentPage === page ? "primary" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              ))}
              <Button
                variant="outline"
                size="sm"
                iconName="ChevronRight"
                iconPosition="right"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                {currentLanguage === 'ar' ? 'التالي' : 'Next'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockMovementTable;