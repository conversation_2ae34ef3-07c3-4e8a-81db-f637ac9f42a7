import React, { useState, useEffect, useMemo } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const MovementTable = ({ movements, onRowExpand, expandedRows, className = "" }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [sortConfig, setSortConfig] = useState({ key: 'timestamp', direction: 'desc' });

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    setCurrentLanguage(savedLanguage);

    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange);
    return () => window.removeEventListener('languageChange', handleLanguageChange);
  }, []);

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedMovements = React.useMemo(() => {
    let sortableMovements = [...movements];
    if (sortConfig.key) {
      sortableMovements.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableMovements;
  }, [movements, sortConfig]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDateTime = (dateTime) => {
    return new Intl.DateTimeFormat(currentLanguage === 'ar' ? 'ar-SA' : 'en-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateTime));
  };

  const getMovementTypeIcon = (type) => {
    switch (type) {
      case 'entry':
        return 'ArrowDown';
      case 'exit':
        return 'ArrowUp';
      case 'transfer':
        return 'ArrowRightLeft';
      default:
        return 'Package';
    }
  };

  const getMovementTypeColor = (type) => {
    switch (type) {
      case 'entry':
        return 'text-success';
      case 'exit':
        return 'text-error';
      case 'transfer':
        return 'text-primary';
      default:
        return 'text-text-secondary';
    }
  };

  const getMovementTypeLabel = (type) => {
    switch (type) {
      case 'entry':
        return currentLanguage === 'ar' ? 'دخول' : 'Entry';
      case 'exit':
        return currentLanguage === 'ar' ? 'خروج' : 'Exit';
      case 'transfer':
        return currentLanguage === 'ar' ? 'تحويل' : 'Transfer';
      default:
        return type;
    }
  };

  const SortableHeader = ({ sortKey, children }) => (
    <th 
      className="px-6 py-3 text-left rtl:text-right text-xs font-medium text-text-secondary uppercase tracking-wider cursor-pointer hover:bg-secondary-50 transition-colors duration-150"
      onClick={() => handleSort(sortKey)}
    >
      <div className="flex items-center space-x-1 rtl:space-x-reverse">
        <span>{children}</span>
        <div className="flex flex-col">
          <Icon 
            name="ChevronUp" 
            size={12} 
            className={`${sortConfig.key === sortKey && sortConfig.direction === 'asc' ? 'text-primary' : 'text-text-muted'}`}
          />
          <Icon 
            name="ChevronDown" 
            size={12} 
            className={`-mt-1 ${sortConfig.key === sortKey && sortConfig.direction === 'desc' ? 'text-primary' : 'text-text-muted'}`}
          />
        </div>
      </div>
    </th>
  );

  return (
    <div className={`bg-surface border border-border rounded-lg shadow-elevation-1 overflow-hidden ${className}`}>
      {/* Desktop Table */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-secondary-50">
            <tr>
              <th className="px-6 py-3 text-left rtl:text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                {currentLanguage === 'ar' ? 'تفاصيل' : 'Details'}
              </th>
              <SortableHeader sortKey="timestamp">
                {currentLanguage === 'ar' ? 'التاريخ والوقت' : 'Date & Time'}
              </SortableHeader>
              <SortableHeader sortKey="productName">
                {currentLanguage === 'ar' ? 'المنتج' : 'Product'}
              </SortableHeader>
              <SortableHeader sortKey="movementType">
                {currentLanguage === 'ar' ? 'النوع' : 'Type'}
              </SortableHeader>
              <SortableHeader sortKey="quantity">
                {currentLanguage === 'ar' ? 'الكمية' : 'Quantity'}
              </SortableHeader>
              <SortableHeader sortKey="unitPrice">
                {currentLanguage === 'ar' ? 'سعر الوحدة' : 'Unit Price'}
              </SortableHeader>
              <SortableHeader sortKey="totalValue">
                {currentLanguage === 'ar' ? 'القيمة الإجمالية' : 'Total Value'}
              </SortableHeader>
              <SortableHeader sortKey="warehouse">
                {currentLanguage === 'ar' ? 'المستودع' : 'Warehouse'}
              </SortableHeader>
              <SortableHeader sortKey="user">
                {currentLanguage === 'ar' ? 'المستخدم' : 'User'}
              </SortableHeader>
            </tr>
          </thead>
          <tbody className="bg-surface divide-y divide-border">
            {sortedMovements.map((movement) => (
              <React.Fragment key={movement.id}>
                <tr className="hover:bg-secondary-50 transition-colors duration-150">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Button
                      variant="ghost"
                      onClick={() => onRowExpand(movement.id)}
                      className="p-1"
                      iconName={expandedRows.includes(movement.id) ? "ChevronDown" : "ChevronRight"}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-primary">
                    {formatDateTime(movement.timestamp)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                        <Icon name="Package" size={16} color="var(--color-primary)" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-text-primary">{movement.productName}</div>
                        <div className="text-xs text-text-secondary">{movement.productSku}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`flex items-center space-x-2 rtl:space-x-reverse ${getMovementTypeColor(movement.movementType)}`}>
                      <Icon name={getMovementTypeIcon(movement.movementType)} size={16} />
                      <span className="text-sm font-medium">{getMovementTypeLabel(movement.movementType)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-primary font-medium">
                    {movement.quantity.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-primary">
                    {formatCurrency(movement.unitPrice)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-text-primary">
                    {formatCurrency(movement.totalValue)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                    {movement.warehouse}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                    {movement.user}
                  </td>
                </tr>
                {expandedRows.includes(movement.id) && (
                  <tr>
                    <td colSpan="9" className="px-6 py-4 bg-secondary-50">
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="text-sm font-medium text-text-primary mb-2">
                              {currentLanguage === 'ar' ? 'تفاصيل إضافية' : 'Additional Details'}
                            </h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-text-secondary">
                                  {currentLanguage === 'ar' ? 'رقم المرجع:' : 'Reference ID:'}
                                </span>
                                <span className="text-text-primary font-medium">{movement.referenceId}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-text-secondary">
                                  {currentLanguage === 'ar' ? 'الفئة:' : 'Category:'}
                                </span>
                                <span className="text-text-primary">{movement.category}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-text-secondary">
                                  {currentLanguage === 'ar' ? 'الموقع:' : 'Location:'}
                                </span>
                                <span className="text-text-primary">{movement.location}</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-text-primary mb-2">
                              {currentLanguage === 'ar' ? 'الملاحظات' : 'Notes'}
                            </h4>
                            <p className="text-sm text-text-secondary">
                              {movement.notes || (currentLanguage === 'ar' ? 'لا توجد ملاحظات' : 'No notes available')}
                            </p>
                          </div>
                        </div>
                        {movement.attachments && movement.attachments.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-text-primary mb-2">
                              {currentLanguage === 'ar' ? 'المرفقات' : 'Attachments'}
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {movement.attachments.map((attachment, index) => (
                                <div key={index} className="flex items-center space-x-2 rtl:space-x-reverse bg-surface border border-border rounded-md px-3 py-2">
                                  <Icon name="Paperclip" size={14} />
                                  <span className="text-sm text-text-primary">{attachment.name}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="lg:hidden">
        {sortedMovements.map((movement) => (
          <div key={movement.id} className="border-b border-border last:border-b-0">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className={`flex items-center space-x-2 rtl:space-x-reverse ${getMovementTypeColor(movement.movementType)}`}>
                  <Icon name={getMovementTypeIcon(movement.movementType)} size={16} />
                  <span className="text-sm font-medium">{getMovementTypeLabel(movement.movementType)}</span>
                </div>
                <Button
                  variant="ghost"
                  onClick={() => onRowExpand(movement.id)}
                  className="p-1"
                  iconName={expandedRows.includes(movement.id) ? "ChevronDown" : "ChevronRight"}
                />
              </div>
              
              <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Icon name="Package" size={18} color="var(--color-primary)" />
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-text-primary">{movement.productName}</div>
                  <div className="text-xs text-text-secondary">{movement.productSku}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-text-secondary">
                    {currentLanguage === 'ar' ? 'الكمية:' : 'Quantity:'}
                  </span>
                  <span className="text-text-primary font-medium ml-2 rtl:mr-2">
                    {movement.quantity.toLocaleString()}
                  </span>
                </div>
                <div>
                  <span className="text-text-secondary">
                    {currentLanguage === 'ar' ? 'القيمة:' : 'Value:'}
                  </span>
                  <span className="text-text-primary font-semibold ml-2 rtl:mr-2">
                    {formatCurrency(movement.totalValue)}
                  </span>
                </div>
                <div>
                  <span className="text-text-secondary">
                    {currentLanguage === 'ar' ? 'المستودع:' : 'Warehouse:'}
                  </span>
                  <span className="text-text-primary ml-2 rtl:mr-2">{movement.warehouse}</span>
                </div>
                <div>
                  <span className="text-text-secondary">
                    {currentLanguage === 'ar' ? 'التاريخ:' : 'Date:'}
                  </span>
                  <span className="text-text-primary ml-2 rtl:mr-2">
                    {formatDateTime(movement.timestamp)}
                  </span>
                </div>
              </div>

              {expandedRows.includes(movement.id) && (
                <div className="mt-4 pt-4 border-t border-border space-y-3">
                  <div>
                    <span className="text-text-secondary text-sm">
                      {currentLanguage === 'ar' ? 'رقم المرجع:' : 'Reference ID:'}
                    </span>
                    <span className="text-text-primary font-medium ml-2 rtl:mr-2 text-sm">
                      {movement.referenceId}
                    </span>
                  </div>
                  <div>
                    <span className="text-text-secondary text-sm">
                      {currentLanguage === 'ar' ? 'المستخدم:' : 'User:'}
                    </span>
                    <span className="text-text-primary ml-2 rtl:mr-2 text-sm">{movement.user}</span>
                  </div>
                  {movement.notes && (
                    <div>
                      <span className="text-text-secondary text-sm block mb-1">
                        {currentLanguage === 'ar' ? 'الملاحظات:' : 'Notes:'}
                      </span>
                      <p className="text-text-primary text-sm">{movement.notes}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {sortedMovements.length === 0 && (
        <div className="text-center py-12">
          <Icon name="Package" size={48} className="mx-auto text-text-muted mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">
            {currentLanguage === 'ar' ? 'لا توجد حركات' : 'No movements found'}
          </h3>
          <p className="text-text-secondary">
            {currentLanguage === 'ar' ? 'لم يتم العثور على حركات تطابق المعايير المحددة' : 'No movements match the selected criteria'}
          </p>
        </div>
      )}
    </div>
  );
};

export default MovementTable;